package com.simonmarkets.sales.fee.rules.service

import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.Source
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.{NetworkDTO, NetworksClient}
import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import com.simonmarkets.sales.fee.rules.common.FeeType._
import com.simonmarkets.sales.fee.rules.common.repository.SalesFeeRuleRepository
import com.simonmarkets.sales.fee.rules.models.{Calculation, EstimateV2Request}
import org.mockito.Mockito._
import org.mockito.{ArgumentMatchers => MM}
import org.scalatest.Matchers._
import org.scalatest.WordSpec
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mockito.MockitoSugar
import simon.Id.NetworkId

import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.language.implicitConversions

class SalesFeeCalculatorServiceSpec extends WordSpec with MockitoSugar with ScalaFutures {
  import SalesFeeCalculatorServiceSpec._

  implicit val traceId: TraceId = TraceId.randomize

  "SalesFeeCalculator" when {

    "estimate" should {

      "be default driven" in new Scope(
        R("1"   , Wholesaler  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("11"  , Wholesaler  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("2"   , HomeOffice  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("22"  , HomeOffice  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("3"   , Simon       , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("33"  , Simon       , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0)) {

        val result = calculator.estimate(
          networkId,
          "ISS2",
          "CTW2",
          None,
          None,
          None).futureValue

        result shouldBe Calculation(
          wholesaler = FeeSchedule(12 -> 2, 24 -> 1),
          homeoffice = FeeSchedule(12 -> 2, 24 -> 1),
          simon      = FeeSchedule(12 -> 2, 24 -> 1),
          faComission = FeeSchedule())
      }

      "merge matched schedules" in new Scope(
        R("1"   , Wholesaler  , '*      , '*    , '*    ) -> FeeSchedule(1 -> 1),
        R("ign" , Wholesaler  , 'ISS2   , 'CT2  , 'CTW2 ) -> FeeSchedule(9 -> 9), // should be ignored
        R("2"   , Wholesaler  , 'ISS1   , '*    , '*    ) -> FeeSchedule(2 -> 2),
        R("3"   , Wholesaler  , '*      , 'CT1  , '*    ) -> FeeSchedule(3 -> 3),
        R("4"   , Wholesaler  , '*      , '*    , 'CTW1 ) -> FeeSchedule(4 -> 4),
        R("5"   , Simon       , '*      , '*    , '*    ) -> FeeSchedule(1 -> 1),
        R("6"   , HomeOffice  , '*      , '*    , '*    ) -> FeeSchedule(1 -> 1)) {

        val result = calculator.estimate(
          networkId,
          "ISS1",
          "CTW1",
          None,
          None,
          None).futureValue

        result shouldBe Calculation(
          wholesaler  = FeeSchedule(1 -> 1, 2 -> 2, 3 -> 3, 4 -> 4),
          homeoffice  = FeeSchedule(1 -> 1),
          simon       = FeeSchedule(1 -> 1),
          faComission = FeeSchedule())
      }

      "merge matched schedules (2)" in new Scope(
        R("1"   , Wholesaler  , '*      , '*    , '*    ) -> FeeSchedule(240 -> 0),
        R("2"   , Wholesaler  , '*      , 'CT1  , '*    ) -> FeeSchedule(12 -> 1  , 24 -> 2, 36 -> 3, 240 -> 4),
        R("3"   , Wholesaler  , '*      , '*    , 'CTW1 ) -> FeeSchedule(12 -> 100),
        R("4"   , Simon       , '*      , '*    , '*    ) -> FeeSchedule(1 -> 1),
        R("5"   , HomeOffice  , '*      , '*    , '*    ) -> FeeSchedule(1 -> 1)) {

        val result = calculator.estimate(
          networkId,
          "ISS1",
          "CTW1",
          None,
          None,
          None).futureValue

        result shouldBe Calculation(
          wholesaler = FeeSchedule(
            12  -> 1,
            24  -> 2,
            36  -> 3,
            240 -> 0),
          homeoffice  = FeeSchedule(1 -> 1),
          simon       = FeeSchedule(1 -> 1),
          faComission = FeeSchedule()
        )
      }
    }

    "estimateBatch" should {

      "be default driven" in new Scope(
        R("1"   , Wholesaler  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("11"  , Wholesaler  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("2"   , HomeOffice  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("22"  , HomeOffice  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("3"   , Simon       , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("33"  , Simon       , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0)) {

        val est1 = EstimateV2Request(Some("Id1"), networkId, "ISS1", "CTW1", None, None, None)

        val est2 = EstimateV2Request(Some("Id2"), networkId2, "ISS2", "CTW2", None, None, None)

        val estReq = Seq(est1, est2)

        val waitDuration: FiniteDuration = Duration(5, "seconds")
        val result = Await.result(calculator.estimateBatch(estReq), waitDuration)

        result.successes.find(_.requestId === "Id1").get.entity shouldBe
          Calculation(
            wholesaler = FeeSchedule(99 -> 0, 199 ->0, 12 -> 2, 24 -> 1),
            homeoffice = FeeSchedule(99 -> 0, 199 ->0, 12 -> 2, 24 -> 1),
            simon      = FeeSchedule(99 -> 0, 199 ->0, 12 -> 2, 24 -> 1),
            faComission = FeeSchedule(),
          )

        result.successes.find(_.requestId === "Id2").get.entity shouldBe
          Calculation(
            wholesaler = FeeSchedule(12 -> 2, 24 -> 1),
            homeoffice = FeeSchedule(12 -> 2, 24 -> 1),
            simon      = FeeSchedule(12 -> 2, 24 -> 1),
            faComission = FeeSchedule(),
          )
      }

      "handle same network in batch request" in new Scope(
        R("1"   , Wholesaler  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("11"  , Wholesaler  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("2"   , HomeOffice  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("22"  , HomeOffice  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("3"   , Simon       , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("33"  , Simon       , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0)) {

        val est1 = EstimateV2Request(Some("Id1"), networkId, "ISS1", "CTW1", None, None, None)

        val est2 = EstimateV2Request(Some("Id2"), networkId, "ISS2", "CTW2", None, None, None)

        val estReq = Seq(est1, est2)

        val waitDuration: FiniteDuration = Duration(5, "seconds")
        val result = Await.result(calculator.estimateBatch(estReq), waitDuration)

        result.successes.find(_.requestId === "Id1").get.entity shouldBe
          Calculation(
            wholesaler = FeeSchedule(99 -> 0, 199 ->0, 12 -> 2, 24 -> 1),
            homeoffice = FeeSchedule(99 -> 0, 199 ->0, 12 -> 2, 24 -> 1),
            simon      = FeeSchedule(99 -> 0, 199 ->0, 12 -> 2, 24 -> 1),
            faComission = FeeSchedule(),
          )

        result.successes.find(_.requestId === "Id2").get.entity shouldBe
          Calculation(
            wholesaler = FeeSchedule(12 -> 2, 24 -> 1),
            homeoffice = FeeSchedule(12 -> 2, 24 -> 1),
            simon      = FeeSchedule(12 -> 2, 24 -> 1),
            faComission = FeeSchedule(),
          )
      }

      "handle negative case" in new Scope() {

        val est1 = EstimateV2Request(Some("Id1"), networkId, "ISS1", "CTW1", None, None, None)

        val est2 = EstimateV2Request(Some("Id2"), networkId2, "ISS2", "CTW2", None, None, None)

        val estReq = Seq(est1, est2)

        val waitDuration: FiniteDuration = Duration(5, "seconds")
        val result = Await.result(calculator.estimateBatch(
          estReq), waitDuration)

        result.failures.find(_.requestId === "Id1").get.failureMessage shouldBe
          "Fee schedule is not defined for simon and structuring and wholesaler and homeOffice components. Please define it to proceed with calculations."

        result.failures.find(_.requestId === "Id2").get.failureMessage shouldBe
          "Fee schedule is not defined for simon and structuring and wholesaler and homeOffice components. Please define it to proceed with calculations."
      }

      "handle mixed scenario case" in new Scope(
        R("11"  , Wholesaler  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("22"  , HomeOffice  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("33"  , Simon       , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0)) {

        val est1 = EstimateV2Request(Some("Id1"), networkId, "ISS1", "CTW1", None, None, None)

        val est2 = EstimateV2Request(Some("Id2"), networkId2, "ISS2", "CTW2", None, None, None)

        val estReq = Seq(est1, est2)

        val waitDuration: FiniteDuration = Duration(5, "seconds")
        val result = Await.result(calculator.estimateBatch(
          estReq), waitDuration)

        result.successes.find(_.requestId === "Id1").get.entity shouldBe
          Calculation(
            wholesaler = FeeSchedule(99 -> 0, 199 ->0),
            homeoffice = FeeSchedule(99 -> 0, 199 ->0),
            simon      = FeeSchedule(99 -> 0, 199 ->0),
            faComission = FeeSchedule(),
          )
        result.failures.find(_.requestId === "Id2").get.failureMessage shouldBe
          "Fee schedule is not defined for simon and structuring and wholesaler and homeOffice components. Please define it to proceed with calculations."
      }

      "should keep ids order" in new Scope(
        R("1"   , Wholesaler  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("11"  , Wholesaler  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("2"   , HomeOffice  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("22"  , HomeOffice  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("3"   , Simon       , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("33"  , Simon       , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0)) {

        val est1 = EstimateV2Request(Some("Id1"), networkId, "ISS1", "CTW1", None, None, None)
        val est2 = EstimateV2Request(Some("Id2"), networkId2, "ISS2", "CTW2", None, None, None)
        val est3 = EstimateV2Request(Some("Id3"), networkId, "ISS1", "CTW1", None, None, None)
        val est4 = EstimateV2Request(Some("Id4"), networkId2, "ISS2", "CTW2", None, None, None)

        val estReq = Seq(est1, est2, est3, est4)

        val waitDuration: FiniteDuration = Duration(5, "seconds")
        val result = Await.result(calculator.estimateBatch(estReq), waitDuration)

        result.successes.map(_.requestId) shouldBe Seq("Id1", "Id2", "Id3", "Id4")
      }

      "should produce ids if they are not preset" in new Scope(
        R("1"   , Wholesaler  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("11"  , Wholesaler  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("2"   , HomeOffice  , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("22"  , HomeOffice  , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0),
        R("3"   , Simon       , '*      , '*    , '*    ) -> FeeSchedule(12 -> 2, 24 -> 1),
        R("33"  , Simon       , 'ISS1   , 'CT1  , 'CTW1 ) -> FeeSchedule(99 -> 0, 199 -> 0)) {

        val est1 = EstimateV2Request(None, networkId, "ISS1", "CTW1", None, None, None)
        val est2 = EstimateV2Request(Some("Id1"), networkId2, "ISS2", "CTW2", None, None, None)
        val est3 = EstimateV2Request(None, networkId, "ISS1", "CTW1", None, None, None)
        val est4 = EstimateV2Request(Some("Id2"), networkId2, "ISS2", "CTW2", None, None, None)

        val estReq = Seq(est1, est2, est3, est4)

        val waitDuration: FiniteDuration = Duration(5, "seconds")
        val result = Await.result(calculator.estimateBatch(estReq), waitDuration)

        result.successes.map(_.requestId) shouldBe Seq("0", "Id1", "2", "Id2")
      }

      "send no such network message when network id not found" in new Scope(){
        when(networksClient.streamAll(ids = Some(List(networkId)))) thenReturn Source(List.empty[Seq[NetworkDTO]])

        val est1 = EstimateV2Request(Some("Id1"), networkId, "ISS1", "CTW1", None, None, None)

        val estReq = Seq(est1)

        val waitDuration: FiniteDuration = Duration(5, "seconds")
        val result = Await.result(calculator.estimateBatch(
          estReq), waitDuration)

        result.failures.find(_.requestId === "Id1").get.failureMessage shouldBe
          "Cannot find network fees for networkId=network-id"
      }
    }
  }

  abstract class Scope(ruleDefs: (R, FeeSchedule)*) {

    lazy val rules: List[SalesFeeRule] = makeRules(ruleDefs).toList

    lazy val networkId = NetworkId("network-id")

    lazy val networkId2 = NetworkId("network-id2")

    lazy val notValidNetId = NetworkId("notValidNetId")

    lazy val network = NetworkDTO(
      id              = networkId,
      networkName     = NetworkId unwrap networkId,
      salesFeeRuleIds = rules map { _.id },
      dtccId          = None,
    )

    lazy val network2 = NetworkDTO(
      id              = networkId2,
      networkName     = NetworkId unwrap networkId2,
      salesFeeRuleIds = rules map { _.id },
      dtccId          = None,
    )

    lazy val networksClient: NetworksClient = {
      val netClient = mock[NetworksClient]
      when(netClient.getById(networkId)) thenReturn Future.successful(network)
      when(netClient.getById(networkId2)) thenReturn Future.successful(network2)
      when(netClient.getById(notValidNetId)) thenReturn Future.failed(new RuntimeException("Network not found"))

      when(netClient.streamAll(ids = Some(List(networkId)))) thenReturn Source(List(Seq(network)))
      when(netClient.streamAll(ids = Some(List(networkId2)))) thenReturn Source(List(Seq(network2)))
      when(netClient.streamAll(ids = Some(List(networkId, networkId2)))) thenReturn Source(List(Seq(network, network2)))
      netClient
    }



    lazy val scrRepo: SalesFeeRuleRepository = {
      val scrRepo = mock[SalesFeeRuleRepository]
      when(scrRepo.findById(MM.any[String])) thenReturn Future.successful(None)
      for {rule <- rules}
        when(scrRepo.findById(MM.eq(rule.id))) thenReturn Future.successful(Some(rule))
      when(scrRepo.findAllById(MM.eq(network.salesFeeRuleIds ++ network2.salesFeeRuleIds))) thenReturn Future.successful(rules)
      when(scrRepo.findAllById(MM.eq(rules map { _.id }))) thenReturn Future.successful(rules)

      scrRepo
    }

    implicit val ec: ExecutionContext = ExecutionContext.global

    implicit val system: ActorSystem = ActorSystem("test")
    implicit val mat: Materializer = Materializer(system)

    lazy val calculator: SalesFeeCalculatorService = new SalesFeeCalculatorService(networksClient, scrRepo)
  }
}

object SalesFeeCalculatorServiceSpec {

  case class R(
      id: String,
      feeType: FeeType,
      issuerKeys: List[String],
      contractTypes: List[String],
      contractTypeWrappers: List[String])

  implicit def sym2Opt(sym: Symbol): List[String] = sym.name match {
    case "*" => Nil
    case _ => List(sym.name)
  }

  implicit def str2Opt(str: String): List[String] = str match {
    case "*" => Nil
    case _ => List(str)
  }

  val issuerKey = 'ISS1

  val contractType = 'CT1

  val contractTypeWrapper = 'CTW1

  val network1Rules = List(
    R("1", Wholesaler, issuerKey, contractType, contractTypeWrapper) -> FeeSchedule(24 -> 1.0),
    R("2", HomeOffice, issuerKey, contractType, contractTypeWrapper) -> FeeSchedule(24 -> 2.0),
    R("3", Simon, issuerKey, contractType, contractTypeWrapper) -> FeeSchedule(24 -> 0.5))

  val network2Rules = List(
    R("1", Wholesaler, issuerKey, contractType, contractTypeWrapper) -> FeeSchedule(24 -> 1.5),
    R("2", HomeOffice, issuerKey, contractType, contractTypeWrapper) -> FeeSchedule(24 -> 2.5),
    R("3", Simon, issuerKey, contractType, contractTypeWrapper) -> FeeSchedule(24 -> 1.0))

  def makeRules(ruleDefs: Iterable[(R, FeeSchedule)]): Iterable[SalesFeeRule] = ruleDefs.toList map {
    case (r, fs) => SalesFeeRule(
      id = r.id,
      name = r.id,
      version = 1,
      deleted = false,
      feeType = r.feeType,
      issuerKeys = r.issuerKeys,
      contractTypes = r.contractTypes,
      contractTypeWrappers = r.contractTypeWrappers,
      fees = fs,
      callable = None,
      fullyProtected = None,
      hasSinglePayment = None,
      nonCallPeriodInMonths = None,
    )
  }
}
