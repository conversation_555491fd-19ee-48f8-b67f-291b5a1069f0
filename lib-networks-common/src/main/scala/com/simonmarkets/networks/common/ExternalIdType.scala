package com.simonmarkets.networks.common

import io.simon.openapi.annotation.{ClassReference, OpenApiType}
import io.simon.openapi.annotation.Field.{Pattern, Ref, Type, TypeArgRef}
import io.simon.openapi.definitions.CommonDefinitions
import simon.Id.NetworkId

case class ExternalIdType(
    @Type(OpenApiType.String)
    @Pattern("^[\\w-]{1,128}$")
    name: String,
    @TypeArgRef(CommonDefinitions.NetworkName)
    matchableNetworks: Set[NetworkId],
    @Type(OpenApiType.String)
    @Pattern("^[\\w-]{1,128}$")
    idpFieldName: String
)

@Ref(ClassReference(classOf[Seq[ExternalIdType]]))
case object ExternalIdTypeBatch