package com.simonmarkets.users

import akka.http.scaladsl.model.Uri
import akka.http.scaladsl.model.Uri.Query
import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.http.HttpEncoder._
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.common.api.request.UpsertUserRequest
import com.simonmarkets.users.common.api.response.{BatchUserUpsertResponse, UserView}

import scala.concurrent.{ExecutionContext, Future}

trait UsersClientV2 {

  /**
   * To be used as warmup to establish connection to client
   * @param traceId trace
   * @return Unit
   */
  def ping(implicit traceId: TraceId): Future[Unit]

  /**
   * Create a single user in SIMON
   * @param request data to create user
   * @param traceId trace
   * @return created user's view
   */
  def insert(request: UpsertUserRequest)(implicit traceId: TraceId): Future[UserView]

  /**
   * Create many users in SIMON
   * @param requests data to create users
   * @param traceId
   * @return
   */
  def insertMany(request: Set[UpsertUserRequest])(implicit traceId: TraceId): Future[BatchUserUpsertResponse]

  def getUserById(userId: String, isICapitalUserIdOpt: Option[Boolean] = None)(implicit traceId: TraceId): Future[Option[UserView]]

}

class HttpUsersClientV2(client: FutureHttpClient, path: String)
  (implicit ec: ExecutionContext, mat: Materializer) extends UsersClientV2 with JsonCodecs with TraceLogging {

  private val url = s"$path/v2/users"

  override def ping(implicit traceId: TraceId): Future[Unit] =
    client.get[String](s"$path/v2/users/healthcheck").map(_ => ())

  override def insert(request: UpsertUserRequest)(implicit traceId: TraceId): Future[UserView] = {
    client.post[UpsertUserRequest, UserView](url, request)
  }

  override def insertMany(request: Set[UpsertUserRequest])(implicit traceId: TraceId): Future[BatchUserUpsertResponse] = {
    client.post[Set[UpsertUserRequest], BatchUserUpsertResponse](s"$url/bulk-upsert", request)
  }

  override def getUserById(userId: String, isICapitalUserIdOpt: Option[Boolean])(implicit
      traceId: TraceId): Future[Option[UserView]] = {
    val query = Query(
      "isICapitalUserId" -> isICapitalUserIdOpt.getOrElse("").toString
    )
    val uri = Uri(s"$url/$userId").withQuery(query)
    client.get[Option[UserView]](uri)
  }

}
