package com.simonmarkets.oauth.client

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.http.scaladsl.model.{ContentType, HttpEntity, MediaTypes, Uri}
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.{FutureHttpClient, HttpEncoder}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.oauth.domain.request.OAuthTokenExchangeRequest
import com.simonmarkets.oauth.domain.response.{AddeparOAuthTokenResponse, OAuthTokenResponse}

import scala.concurrent.{ExecutionContext, Future}


object AddeparOAuthClient {
  def apply(config: OAuthClientConfig)
    (implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): AddeparOAuthClient = {
    val client = new FutureHttpClient(Http(), config.httpClient)
    new AddeparOAuthClient(client, config.apiPrefix, config.clientId, config.clientSecret)
  }
}

class AddeparOAuthClient(httpClient: FutureHttpClient, baseUrl: String, clientId: String, clientSecret: String)(implicit ec: ExecutionContext, mat: Materializer)
  extends OAuthClient with JsonCodecs with TraceLogging {

  override def exchange(authCode: String, redirectUrl: String)(implicit traceId: TraceId, user: UserACL): Future[AddeparOAuthTokenResponse] = {
    val request = OAuthTokenExchangeRequest(
      clientId = clientId,
      clientSecret = clientSecret,
      redirectUrl = redirectUrl,
      code = authCode,
      grantType = "authorization_code"
    )
    val uri = Uri(s"$baseUrl/api/public/oauth2/token")

    implicit val httpEncoder: HttpEncoder[OAuthTokenExchangeRequest] = (data: OAuthTokenExchangeRequest) => {
      val jsonString = data.asJson.noSpaces
      val contentType: ContentType = ContentType(MediaTypes.`application/x-www-form-urlencoded`)
      HttpEntity(contentType, jsonString.getBytes)
    }

    httpClient.post[OAuthTokenExchangeRequest, AddeparOAuthTokenResponse](
      uri = uri,
      data = request,
    )
  }

  override def retrieve(implicit traceId: TraceId, userACL: UserACL): Future[OAuthTokenResponse] = ???

  override def refresh(implicit traceId: TraceId, userACL: UserACL): Future[OAuthTokenResponse] = ???
}
