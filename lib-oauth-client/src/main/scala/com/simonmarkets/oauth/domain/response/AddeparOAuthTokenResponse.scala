package com.simonmarkets.oauth.domain.response

import com.simonmarkets.oauth.domain.TokenIssuer

case class AddeparOAuthTokenResponse(
    override val accessToken: Option[String],
    override val refreshToken: Option[String],
    override val tokenType: Option[String],
    override val expiresIn: Int,
    override val tokenIssuer: Option[TokenIssuer],
    addeparFirm: Option[String],
    addeparSubdomain: Option[String]
) extends OAuthTokenResponse
