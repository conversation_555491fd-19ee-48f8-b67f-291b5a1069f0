package com.goldmansachs.marquee.pipg

import org.apache.commons.lang3.SerializationUtils
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{FunSpec, Matchers}
import simon.Id.NetworkId

class UserACLSpec extends FunSpec with MockitoSugar with Matchers {

  val role1 = "role1"
  val role2 = "role2"

  val capability1 = "capability1"
  val capability2 = "capability2"
  val capability3 = "capability3"

  val networkId = NetworkId("networkId")

  val user = mock[User]
  when(user.id) thenReturn "id"
  when(user.networkId) thenReturn networkId
  when(user.dynamicRoles) thenReturn Set.empty[String]
  when(user.roles) thenReturn Set.empty[UserRole]
  when(user.isActive) thenReturn true
  when(user.purviewLicenses) thenReturn Set.empty[License]
  when(user.purviewNsccCodes) thenReturn Set.empty[String]
  when(user.groups) thenReturn Map.empty[String, Set[String]]
  when(user.icnGroups) thenReturn Set.empty[String]
  when(user.icnRoles) thenReturn Set.empty[String]
  when(user.passport) thenReturn Map.empty[String, Int]

  val network = mock[Network]
  when(network.id) thenReturn networkId
  when(network.approverSet) thenReturn Map.empty[String, List[List[String]]]
  when(network.purviewNetworks) thenReturn None
  when(network.purviews) thenReturn None
  when(network.dynamicRoles) thenReturn Set.empty[String]
  when(network.purviews) thenReturn None
  when(network.networkTypes) thenReturn Some(List(NetworkType.Admin))
  when(network.productTypeCertificationRequirements) thenReturn None

  describe("UserACL") {
    it("When network has custom roles configured and user has a custom role, user should have the associated capabilities") {
      when(network.customRolesConfig) thenReturn Set(CustomRoleDefinition(role1, Set(capability1)), CustomRoleDefinition(role2, Set(capability2, capability3)))
      when(user.customRoles) thenReturn Set(role2)

      val userACL = UserACL(user, network)
      userACL.capabilities shouldBe Set(capability2, capability3)
    }

    it("When network has custom roles configured and user has no custom roles, user should have no capabilities") {
      when(network.customRolesConfig) thenReturn Set(CustomRoleDefinition(role1, Set(capability1)), CustomRoleDefinition(role2, Set(capability2, capability3)))
      when(user.customRoles) thenReturn Set.empty[String]

      val userACL = UserACL(user, network)
      userACL.capabilities shouldBe Set.empty
    }

    it("When network has custom roles configured and user has multiple custom roles, user should have all associated capabilities") {
      when(network.customRolesConfig) thenReturn Set(CustomRoleDefinition(role1, Set(capability1)), CustomRoleDefinition(role2, Set(capability2, capability3)))
      when(user.customRoles) thenReturn Set(role1, role2)

      val userACL = UserACL(user, network)
      userACL.capabilities shouldBe Set(capability1, capability2, capability3)
    }

    it("When user is not active, user should have no capabilities") {
      when(network.customRolesConfig) thenReturn Set(CustomRoleDefinition(role1, Set(capability1)), CustomRoleDefinition(role2, Set(capability2, capability3)))
      when(user.customRoles) thenReturn Set(role2)
      when(user.isActive) thenReturn false

      val userACL = UserACL(user, network)
      userACL.capabilities shouldBe Set.empty
    }

    describe("allAccessibleLocations") {
      it("check userAcl has build all locations") {
        when(network.customRolesConfig) thenReturn Set.empty[CustomRoleDefinition]
        when(user.locations) thenReturn Set("B", "G")
        when(network.locationHierarchy) thenReturn Some(
          LocationNode("A",
            children = Set(
              LocationNode("B", children = Set(LocationNode("D", children = Set.empty), LocationNode("E", Set.empty))),
              LocationNode("C", children = Set(LocationNode("F", children = Set.empty), LocationNode("G", Set.empty)))
            )
          )
        )
        val userACL = UserACL(user, network)
        userACL.allAccessibleLocations shouldBe Set("B", "D", "E", "G")
      }

      it("check userAcl has build locations, when networkHierachy is absent") {
        when(network.customRolesConfig) thenReturn Set[CustomRoleDefinition]()
        when(user.locations) thenReturn Set("B", "G")
        when(network.locationHierarchy) thenReturn None
        val userACL = UserACL(user, network)
        userACL.allAccessibleLocations shouldBe Set("B", "G")
      }
    }

    describe("object serialization for storing in cache") {
      it("check object can be serialized and deserialized") {
        val userACL = UserACL(
          userId = "user.userId",
          networkId = NetworkId("networkId"),
          lastVisitedAt = None,
          email = "",
          firstName = "",
          lastName = "",
          distributorId = None,
          omsId = None,
          tradewebEligible = false,
          regSEligible = false,
          isActive = Some(true),
          roles = Set(UserRole.EqPIPGGSAdmin),
          networkTypes = Some(List(NetworkType.Issuer, NetworkType.BrokerDealer)),
          licenses = Set(License.NPN("12345"))
        )

        val serialized = SerializationUtils.serialize(userACL)
        val origin = SerializationUtils.deserialize[UserACL](serialized)
        origin shouldBe userACL
      }
    }

    //TODO: APPSERV-62935 remove test
    it("ACL NetworkSwap") {
      when(network.customRolesConfig) thenReturn Set(CustomRoleDefinition(role1, Set(capability1)))
      when(user.customRoles) thenReturn Set(role2)

      val userACL = UserACL(user, network)
      userACL.networkTypes shouldBe Some(List(NetworkType.SMAManager))
    }
  }
}
