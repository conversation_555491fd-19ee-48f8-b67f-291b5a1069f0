package com.simonmarkets.users.domain

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait TokenProvider extends EnumEntry

object TokenProvider extends ProductEnums[TokenProvider] {
  case object Addepar extends TokenProvider

  override def Values: Seq[TokenProvider] = Seq(Addepar)

//  implicit val unmarshaller: Unmarshaller[String, TokenProvider] = Unmarshaller.strict[String, TokenProvider](
//    TokenProvider(_)
//  )
  @EnumValues("Addepar")
  case object Ref extends Reference
}
