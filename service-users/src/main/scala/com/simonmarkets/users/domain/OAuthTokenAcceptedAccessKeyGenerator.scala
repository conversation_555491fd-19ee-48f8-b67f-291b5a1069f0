package com.simonmarkets.users.domain

import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.capabilities.OAuthTokenCapabilities._

object OAuthTokenAcceptedAccessKeyGenerator extends AcceptedAccessKeysGenerator[OAuthToken] {

  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[OAuthToken]] = Map(
    Admin -> AcceptedKeyBuilder(buildAdminKeys),
    ViewTokenViaUserId -> AcceptedKeyBuilder(buildUserKeys),
    EditTokenViaUserId -> AcceptedKeyBuilder(buildUserKeys),
  )

  private def buildUserKeys(capability: String, token: OAuthToken): Set[String] = {
    if (token.userId.nonEmpty) Set(s"$capability:${token.userId}") else Set.empty
  }
}
