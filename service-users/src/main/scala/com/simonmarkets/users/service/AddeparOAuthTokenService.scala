package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.OAuthTokenCapabilities._
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.oauth.client.AddeparOAuthClient
import com.simonmarkets.oauth.domain.request.OAuthTokenRequest
import com.simonmarkets.users.domain.{OAuthToken, OAuthTokenAcceptedAccessKeyGenerator}
import com.simonmarkets.users.repository.OAuthTokenRepository
import io.scalaland.chimney.dsl.TransformerOps

import java.time.Instant

import scala.concurrent.{ExecutionContext, Future}

class AddeparOAuthTokenService(addeparClient: AddeparOAuthClient, tokenRepo: OAuthTokenRepository)(implicit ec: ExecutionContext) extends OAuthTokenService {

  override def exchangeAndStoreToken(req: OAuthTokenRequest)(implicit traceId: TraceId, user: UserACL): Future[Boolean] = {
    for {
      tokenResponse <- addeparClient.exchange(req.authCode, req.redirectUrl)
      availableAccessKeys = getAvailableAccessKeysForCapabilities(EditCapabilities, user)
      token = tokenResponse.into[OAuthToken]
        .withFieldConst(_.userId, user.userId)
        .withFieldConst(_.acceptedAccessKeys, Set.empty[String])
        .withFieldConst(_.updatedAt, Instant.now)
        .transform
      tokenWithEntitlements = token.copy(acceptedAccessKeys = OAuthTokenAcceptedAccessKeyGenerator.getAcceptedAccessKeys(token))
      upsert <- tokenRepo.storeToken(tokenWithEntitlements, availableAccessKeys)
    } yield upsert
  }

  override def getStoredToken(implicit traceId: TraceId, user: UserACL): Future[OAuthToken] = {
    val availableAccessKeys = getAvailableAccessKeysForCapabilities(EditCapabilities, user)
    val tokenOpt = tokenRepo.retrieveToken(user.userId, availableAccessKeys)

    tokenOpt.flatMap {
      case Some(token) => Future.successful(token)
      case None => Future.failed(HttpError.notFound("No access token found for user"))
    }
  }
}
