package com.simonmarkets.users.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.domain.OAuthToken
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.Filters.{and, in, eq => equal}
import org.mongodb.scala.model.ReplaceOptions

import scala.concurrent.{ExecutionContext, Future}

trait OAuthTokenRepository {
  def storeToken(token: OAuthToken, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Boolean]

  def retrieveToken(userId: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Option[OAuthToken]]
}

class OAuthTokenRepositoryImpl(
    collection: MongoCollection[OAuthToken]
)(implicit ec: ExecutionContext)
extends OAuthTokenRepository {
  private def accessKeysCondition(availableAccessKeys: Set[String]): Bson =
    in("acceptedAccessKeys", availableAccessKeys.toSeq: _*)

  override def storeToken(token: OAuthToken, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Boolean] = {
    collection
      .replaceOne(
        and(
          equal("userId", token.userId),
          accessKeysCondition(availableAccessKeys)
        ),
        token,
        new ReplaceOptions().upsert(true)
      )
      .toFuture()
      .map { _.getModifiedCount > 0 }
  }

  override def retrieveToken(userId: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Option[OAuthToken]] = {
    collection.find(
        and(
          equal("userId", userId),
          accessKeysCondition(availableAccessKeys)
        )
      )
      .comment(traceId.traceId)
      .headOption
  }
}

object OAuthTokenRepository {
  val registry: CodecRegistry = fromRegistries(fromProviders(
    Macros.createCodecProviderIgnoreNone[OAuthToken]
    ),
    DEFAULT_CODEC_REGISTRY)
}
