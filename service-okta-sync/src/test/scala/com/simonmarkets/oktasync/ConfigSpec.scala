package com.simonmarkets.oktasync

import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.simonmarkets.utils.config.resolvers.ConfigResolver
import com.typesafe.config.ConfigFactory
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import pureconfig.generic.auto._


class ConfigSpec extends WordSpec with Matchers with MockitoSugar {

  class TestResolver extends ConfigResolver {
    override def prefix: String = "sm"

    override def resolve(path: String): String =
      path match {
        case "applicationconfig-mongo-auth-pipg" =>
          """
            |   {
            |      url = "mongoUrl"
            |      authentication {
            |        type = "password"
            |        user = "alpha-pipg-user"
            |        password = "password"
            |        database = "admin"
            |      }
            |    }""".stripMargin
        case other => other
      }

    override def resolveBinary(path: String): Array[Byte] = ???
  }

  "Configs" should {
    "not blow up" in {
      val configNames = Set("alpha.conf", "qa.conf", "prod.conf")
      configNames.foreach { rawConfig =>
        val config = ConfigFactory.load(rawConfig).resolveSecrets(List(new TestResolver))
        pureconfig.loadConfigOrThrow[OktaSyncConfig](config)
      }
    }
  }
}