package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.entitlements.{HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object NotificationsCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "Notifications"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewNotificationsViaOwnerCapability: Capability = Capability("viewNotificationsViaOwner", "", assetClasses)
  val EditNotificationsViaOwnerCapability: Capability = Capability("editNotificationsViaOwner", "", assetClasses)

  val ViewAdvisorAccountNamesLifecycleNotificationsCapability: Capability = Capability("viewAdvisorAccountNamesLifecycleNotifications", "", assetClasses)
  val HideHistoricalHoldingsLifecycleNotificationsCapability: Capability = Capability("hideHistoricalHoldingsLifecycleNotifications", "", assetClasses)
  val HideManagedAccountHoldingsInLifecycleNotificationsCapability: Capability = Capability("hideManagedAccountHoldingsInLifecycleNotifications", "", assetClasses)

  val ViewNotificationsViaOwner: String = ViewNotificationsViaOwnerCapability.name
  val EditNotificationsViaOwner: String = EditNotificationsViaOwnerCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(ViewNotificationsViaOwnerCapability, AdminCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(EditNotificationsViaOwnerCapability, AdminCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++
  DetailedEditCapabilities ++
  Set(
    ViewAdvisorAccountNamesLifecycleNotificationsCapability,
    HideHistoricalHoldingsLifecycleNotificationsCapability,
    HideManagedAccountHoldingsInLifecycleNotificationsCapability
  )
}
