package com.simonmarkets.capabilities


import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildExternalIdKeys, buildUserPurviewKeys}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder, AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedViewCapabilities}
import simon.Id.NetworkId

object AltHoldingsCapabilities extends Capabilities with HasDetailedViewCapabilities {
  override val DomainName: String = "AltHoldings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AlternativesAssetClass))

  val ViewAltHoldingViaNetworkCapability: Capability = Capability(
    "viewAltHoldingViaNetwork",
    "Allows a user to view a network's alt holdings",
    assetClasses
  )

  val ViewAltHoldingViaPurviewCapability: Capability = Capability(
    "viewAltHoldingViaPurview",
    "Allows a user to view a purviewed network's alt holdings",
    assetClasses
  )

  val ViewAltHoldingViaLocationCapability: Capability = Capability(
    "viewAltHoldingViaLocation",
    "Allows a user to view an alt holding if the holding is available in one of the user's locations",
    assetClasses
  )

  val ViewAltHoldingViaLocationHierarchyCapability: Capability = Capability(
    "viewAltHoldingViaLocationHierarchy",
    "Allows a user to view an alt holding if the holding is available in one of the locations in the user's location hierarchy",
    assetClasses
  )

  val ViewAltHoldingViaFaNumberCapability: Capability = Capability(
    "viewAltHoldingViaFaNumber",
    "Allows a user to view an alt holding based on their fa number",
    assetClasses
  )


  val ViewAltHoldingViaDistributorIdCapability: Capability = Capability(
    "viewAltHoldingViaDistributorId",
    "Allows a user to view an alt holding based on their distributor id",
    assetClasses
  )

  val ViewAltHoldingViaUserExternalIdCapability: Capability = Capability(
    "viewAltHoldingViaUserExternalId",
    "Allows a user to view an alt holding based on their available external user ids",
    assetClasses
  )

  val UploadAltHoldingSnapshotViaNetworkCapability: Capability = Capability(
    "uploadAltHoldingSnapshotViaNetwork",
    "Allows a user to upload a holding snapshot for a network",
    assetClasses
  )

  val UploadAltHoldingSnapshotViaPurviewCapability: Capability = Capability(
    "uploadAltHoldingSnapshotViaPurview",
    "Allows a user to upload a holding snapshot for a purviewed network",
    assetClasses
  )

  val ViewAltHoldingSnapshotViaNetworkCapability: Capability = Capability(
    "viewAltHoldingSnapshotViaNetwork",
    "Allows a user to view a holding snapshot for a network",
    assetClasses
  )

  val ViewAltHoldingSnapshotViaPurviewCapability: Capability = Capability(
    "viewAltHoldingSnapshotViaPurview",
    "Allows a user to view a holding snapshot for a purviewed network",
    assetClasses
  )

  override val DetailedViewCapabilities: Set[Capability] = Set(
    ViewAltHoldingViaNetworkCapability,
    ViewAltHoldingViaPurviewCapability,
    ViewAltHoldingViaLocationCapability,
    ViewAltHoldingViaLocationHierarchyCapability,
    ViewAltHoldingViaFaNumberCapability,
    ViewAltHoldingViaDistributorIdCapability,
    ViewAltHoldingViaUserExternalIdCapability
  )

  val DetailedViewAltHoldingSnapshotCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewAltHoldingSnapshotViaNetworkCapability,
    ViewAltHoldingSnapshotViaPurviewCapability
  )

  val DetailedUploadAltHoldingSnapshotCapabilities: Set[Capability] = Set(
    AdminCapability,
    UploadAltHoldingSnapshotViaNetworkCapability,
    UploadAltHoldingSnapshotViaNetworkCapability,
  )

  /** Defines accepted access keys for snapshot upload. Tasks are not bound to networks,
   * so the keys are generated from Network instead of ProcessingTask */
  val AltSnapshotAcceptedAccessKeys: AcceptedAccessKeysGenerator[NetworkId] = new AcceptedAccessKeysGenerator[NetworkId] {
    override val capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[NetworkId]] = Map(
      AdminCapability.name -> acceptedKeyBuilder,
      UploadAltHoldingSnapshotViaNetworkCapability.name -> acceptedKeyBuilder.add(networkId => networkId),
      UploadAltHoldingSnapshotViaPurviewCapability.name -> acceptedKeyBuilder.add(networkId => networkId),
      ViewAltHoldingSnapshotViaNetworkCapability.name -> acceptedKeyBuilder.add(networkId => networkId),
      ViewAltHoldingSnapshotViaPurviewCapability.name -> acceptedKeyBuilder.add(networkId => networkId)
    )
  }

  /** Defines available access keys for snapshot upload */
  val AltSnapshotAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      AdminCapability.name -> availableKeyBuilder,
      ViewAltHoldingSnapshotViaNetworkCapability.name -> availableKeyBuilder.add(_.networkId),
      UploadAltHoldingSnapshotViaNetworkCapability.name -> availableKeyBuilder.add(_.networkId),
      ViewAltHoldingSnapshotViaPurviewCapability.name -> AvailableKeyBuilder(buildUserPurviewKeys),
      UploadAltHoldingSnapshotViaPurviewCapability.name -> AvailableKeyBuilder(buildUserPurviewKeys)
    )
  }

  /** Defines available access keys for alt holding records */
  val AltHoldingsAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      ViewAltHoldingViaNetworkCapability.name -> availableKeyBuilder.add(_.networkId),
      ViewAltHoldingViaPurviewCapability.name -> availableKeyBuilder.addList(_.userPurviewIds.map(_.toString)),
      ViewAltHoldingViaLocationCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.locations),
      ViewAltHoldingViaLocationHierarchyCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.allAccessibleLocations),
      ViewAltHoldingViaFaNumberCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.faNumbers),
      ViewAltHoldingViaDistributorIdCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.distributorId.toList),
      ViewAltHoldingViaUserExternalIdCapability.name -> AvailableKeyBuilder(buildExternalIdKeys)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++
    DetailedViewAltHoldingSnapshotCapabilities ++
    DetailedUploadAltHoldingSnapshotCapabilities
}
