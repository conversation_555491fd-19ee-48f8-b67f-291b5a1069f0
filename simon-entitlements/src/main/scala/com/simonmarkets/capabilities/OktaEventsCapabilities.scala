package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasEditCapabilities, HasViewCapabilities}

object OktaEventsCapabilities extends Capabilities with HasViewCapabilities with HasEditCapabilities{
  override val DomainName = "OktaEvents"



  override val ViewCapabilities: Set[String] = Set(
    Admin
  )

  override val EditCapabilities: Set[String] = Set(
    Admin
  )

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = Set(AdminCapability)
}
