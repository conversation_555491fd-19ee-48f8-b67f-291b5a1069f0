package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities._
import com.simonmarkets.entitlements.{HasDetailedViewCapabilities, HasViewCapabilities}

object ApprovalsCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities {
  override val DomainName = "Approvals"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AnnuitiesAssetClass))

  val ViewApprovalViaNetworkCapability: Capability = Capability("viewApprovalViaNetwork", "", assetClasses)
  val ViewApprovalViaLocationCapability: Capability = Capability("viewApprovalViaLocation", "", assetClasses)
  val ViewApprovalViaOwnerCapability: Capability = Capability("viewApprovalViaOwner", "", assetClasses)

  val ViewApprovalViaNetwork:String = ViewApprovalViaNetworkCapability.name
  val ViewApprovalViaLocation:String = ViewApprovalViaLocationCapability.name
  val ViewApprovalViaOwner:String = ViewApprovalViaOwnerCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewApprovalViaNetworkCapability, ViewApprovalViaLocationCapability, ViewApprovalViaOwnerCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities
}
