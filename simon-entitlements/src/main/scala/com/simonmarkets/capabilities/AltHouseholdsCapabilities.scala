package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildExternalIdKeys, buildUserPurviewKeys}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder, AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedViewCapabilities}
import simon.Id.NetworkId

object AltHouseholdsCapabilities extends Capabilities with HasDetailedViewCapabilities {
  override val DomainName: String = "AltHouseholds"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AlternativesAssetClass))

  val ViewAltHouseholdViaNetworkCapability: Capability = Capability(
    "viewAltHouseholdViaNetwork",
    "Allows a user to view a network's alt households",
    assetClasses
  )

  val ViewAltHouseholdViaPurviewCapability: Capability = Capability(
    "viewAltHouseholdViaPurview",
    "Allows a user to view a purviewed network's alt households",
    assetClasses
  )

  val ViewAltHouseholdViaLocationCapability: Capability = Capability(
    "viewAltHouseholdViaLocation",
    "Allows a user to view an alt household if the household is available in one of the user's locations",
    assetClasses
  )

  val ViewAltHouseholdViaLocationHierarchyCapability: Capability = Capability(
    "viewAltHouseholdViaLocationHierarchy",
    "Allows a user to view an alt household if the household is available in one of the locations in the user's location hierarchy",
    assetClasses
  )

  val ViewAltHouseholdViaFaNumberCapability: Capability = Capability(
    "viewAltHouseholdViaFaNumber",
    "Allows a user to view an alt household based on their fa number",
    assetClasses
  )


  val ViewAltHouseholdViaDistributorIdCapability: Capability = Capability(
    "viewAltHouseholdViaDistributorId",
    "Allows a user to view an alt household based on their distributor id",
    assetClasses
  )

  val ViewAltHouseholdViaUserExternalIdCapability: Capability = Capability(
    "viewAltHouseholdViaUserExternalId",
    "Allows a user to view an alt household based on their available external user ids",
    assetClasses
  )

  override val DetailedViewCapabilities: Set[Capability] = Set(
    ViewAltHouseholdViaNetworkCapability,
    ViewAltHouseholdViaPurviewCapability,
    ViewAltHouseholdViaLocationCapability,
    ViewAltHouseholdViaLocationHierarchyCapability,
    ViewAltHouseholdViaFaNumberCapability,
    ViewAltHouseholdViaDistributorIdCapability,
    ViewAltHouseholdViaUserExternalIdCapability
  )

  /** Defines available access keys for alt household records */
  val AltHouseholdsAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      ViewAltHouseholdViaNetworkCapability.name -> availableKeyBuilder.add(_.networkId),
      ViewAltHouseholdViaPurviewCapability.name -> availableKeyBuilder.addList(_.userPurviewIds.map(_.toString)),
      ViewAltHouseholdViaLocationCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.locations),
      ViewAltHouseholdViaLocationHierarchyCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.allAccessibleLocations),
      ViewAltHouseholdViaFaNumberCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.faNumbers),
      ViewAltHouseholdViaDistributorIdCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.distributorId.toList),
      ViewAltHouseholdViaUserExternalIdCapability.name -> AvailableKeyBuilder(buildExternalIdKeys)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities
}