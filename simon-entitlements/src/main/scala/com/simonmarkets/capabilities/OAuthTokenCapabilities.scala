package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGuidKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object OAuthTokenCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities with AvailableAccessKeysGenerator {

  override val DomainName: String = "OAuthTokens"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewTokenViaUserIdCapability: Capability = Capability("viewOAuthTokenViaUserId", "", assetClasses)
  val EditTokenViaUserIdCapability: Capability = Capability("editOAuthTokenViaUserId", "", assetClasses)

  val ViewTokenViaUserId: String = ViewTokenViaUserIdCapability.name
  val EditTokenViaUserId: String = EditTokenViaUserIdCapability.name

  override def DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewTokenViaUserIdCapability)

  override def DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditTokenViaUserIdCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewTokenViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    EditTokenViaUserId -> AvailableKeyBuilder(buildGuidKeys)
  )

}
