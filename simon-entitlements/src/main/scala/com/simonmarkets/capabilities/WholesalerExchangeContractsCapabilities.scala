package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkKeys, buildUserPurviewKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

object WholesalerExchangeContractsCapabilities extends Capabilities with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "WholesalerExchangeContracts"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewWholesalerExchangeContractsViaNetworkCapability: Capability =
    Capability("viewWholesalerExchangeContractsViaNetwork", "allows user to view WholesalerExchange contracts within the same network", assetClasses)
  val EditWholesalerExchangeContractsViaNetworkCapability: Capability =
    Capability("editWholesalerExchangeContractsViaNetwork", "allows user to edit WholesalerExchange contracts within the same network", assetClasses)

  override val DetailedViewCapabilities = Set(ViewWholesalerExchangeContractsViaNetworkCapability, AdminCapability)
  override val DetailedEditCapabilities = Set(EditWholesalerExchangeContractsViaNetworkCapability, AdminCapability)

  val availableAccessKeysGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewWholesalerExchangeContractsViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      EditWholesalerExchangeContractsViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}
