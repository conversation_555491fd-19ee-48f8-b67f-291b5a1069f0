package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.capabilities.utils.CapabilityDefinitionImpl.macroCapability
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGuidKeys, undecoratedKeyBuilder}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasViewCapabilities}

@CapabilityDefinition
object DeveloperHubCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "DeveloperHub"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewDeveloperHubServiceViaGuid = macroCapability
  val ViewDeveloperHubServiceViaGuidCapability: Capability = Capability(ViewDeveloperHubServiceViaGuid,
    "view a service as an external user", assetClasses)

  val ViewDeveloperHubRouteViaGuid = macroCapability
  val ViewDeveloperHubRouteViaGuidCapability: Capability = Capability(ViewDeveloperHubRouteViaGuid,
    "view a route as an external user", assetClasses)

  val ViewDeveloperHubUserPreferencesViaGuid = macroCapability
  val ViewDeveloperHubUserPreferencesViaGuidCapability: Capability = Capability(ViewDeveloperHubUserPreferencesViaGuid,
    "view a user's own preferences", assetClasses)

  val EditDeveloperHubUserPreferencesViaGuid = macroCapability
  val EditDeveloperHubUserPreferencesViaGuidCapability: Capability = Capability(EditDeveloperHubUserPreferencesViaGuid,
    "edit a user's own preferences", assetClasses)

  val ViewDeveloperHubUserSettingsViaGuid = macroCapability
  val ViewDeveloperHubUserSettingsViaGuidCapability: Capability = Capability(ViewDeveloperHubUserSettingsViaGuid,
    "view a user's own settings", assetClasses)

  override def DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewDeveloperHubServiceViaGuidCapability,
    ViewDeveloperHubRouteViaGuidCapability,
    ViewDeveloperHubUserPreferencesViaGuidCapability,
    ViewDeveloperHubUserSettingsViaGuidCapability,
  )
  override def DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability,
    EditDeveloperHubUserPreferencesViaGuidCapability,
  )

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewDeveloperHubServiceViaGuid -> AvailableKeyBuilder(buildGuidKeys),
    ViewDeveloperHubRouteViaGuid -> AvailableKeyBuilder(buildGuidKeys),
    ViewDeveloperHubUserPreferencesViaGuid -> AvailableKeyBuilder(buildGuidKeys),
    EditDeveloperHubUserPreferencesViaGuid -> AvailableKeyBuilder(buildGuidKeys),
    ViewDeveloperHubUserSettingsViaGuid -> AvailableKeyBuilder(buildGuidKeys),
  )
}
