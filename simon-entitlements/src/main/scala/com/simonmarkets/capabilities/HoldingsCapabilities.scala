package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities._
import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.capabilities.utils.CapabilityDefinitionImpl.macroCapability
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements._
import simon.Id.NetworkId

@CapabilityDefinition
object HoldingsCapabilities extends Capabilities {
  override val DomainName = "Holdings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewHoldingViaNetwork: String = macroCapability
  val ViewHoldingViaLocation: String = macroCapability
  val ViewHoldingViaLocationHierarchy: String = macroCapability
  val ViewHoldingViaFaNumber: String = macroCapability
  val ViewHoldingViaPurview: String = macroCapability
  val ViewHoldingViaDistributorId: String = macroCapability
  val ViewHoldingViaUserExternalId: String = macroCapability
  val ViewHoldingViaNetworkCapability: Capability = Capability(ViewHoldingViaNetwork, "", assetClasses)
  val ViewHoldingViaLocationCapability: Capability = Capability(ViewHoldingViaLocation, "", assetClasses)
  val ViewHoldingViaLocationHierarchyCapability: Capability = Capability(ViewHoldingViaLocationHierarchy, "", assetClasses)
  val ViewHoldingViaFaNumberCapability: Capability = Capability(ViewHoldingViaFaNumber, "", assetClasses)
  val ViewHoldingViaCustodianFaNumberCapability: Capability = Capability("viewHoldingViaCustodianFaNumber", "Allow user to view a holding based on their custodian fa number", assetClasses)
  val ViewHoldingViaPurviewCapability: Capability = Capability(ViewHoldingViaPurview, "", assetClasses)
  val ViewHoldingViaDistributorIdCapability: Capability = Capability(ViewHoldingViaDistributorId, "", assetClasses)
  val ViewHoldingViaUserExternalIdCapability: Capability = Capability(
    ViewHoldingViaUserExternalId,
    "Allow user to view a holding based on their available external user ids",
    assetClass = assetClasses
  )

  val ViewHoldingSnapshotViaNetwork: String = macroCapability
  val UploadHoldingSnapshotViaNetwork: String = macroCapability
  val ViewHoldingSnapshotViaPurview: String = macroCapability
  val UploadHoldingSnapshotViaPurview: String = macroCapability
  val ViewHoldingSnapshotViaNetworkCapability: Capability = Capability(ViewHoldingSnapshotViaNetwork, "", assetClasses)
  val UploadHoldingSnapshotViaNetworkCapability: Capability = Capability(UploadHoldingSnapshotViaNetwork, "", assetClasses)
  val ViewHoldingSnapshotViaPurviewCapability: Capability = Capability(ViewHoldingSnapshotViaPurview, "", assetClasses)
  val UploadHoldingSnapshotViaPurviewCapability: Capability = Capability(UploadHoldingSnapshotViaPurview, "", assetClasses)

  val ViewHoldingBasedLifecycleNotification: String = macroCapability
  val ViewHoldingBasedLifecycleNotificationCapability: Capability = Capability(ViewHoldingBasedLifecycleNotification, "", assetClasses)

  val ViewHoldingCapabilities: Set[String] = Set(
    ViewHoldingViaNetwork,
    ViewHoldingViaLocation,
    ViewHoldingViaLocationHierarchy,
    ViewHoldingViaFaNumber,
    ViewHoldingViaCustodianFaNumberCapability.name,
    ViewHoldingViaPurview,
    ViewHoldingViaDistributorId,
    ViewHoldingViaUserExternalId
  )
  val ViewHoldingSnapshotCapabilities: Set[String] = Set(Admin, ViewHoldingSnapshotViaNetwork, ViewHoldingSnapshotViaPurview)
  val UploadHoldingSnapshotCapabilities: Set[String] = Set(Admin, UploadHoldingSnapshotViaNetwork, UploadHoldingSnapshotViaPurview)
  val ViewHoldingBasedNotificationCapabilities: Set[String] = Set(ViewHoldingBasedLifecycleNotification)

  val DetailedViewHoldingCapabilities: Set[Capability] = Set(ViewHoldingViaNetworkCapability, ViewHoldingViaLocationCapability, ViewHoldingViaLocationHierarchyCapability, ViewHoldingViaFaNumberCapability, ViewHoldingViaCustodianFaNumberCapability, ViewHoldingViaPurviewCapability, ViewHoldingViaDistributorIdCapability, ViewHoldingViaUserExternalIdCapability)
  val DetailedViewHoldingSnapshotCapabilities: Set[Capability] = Set(AdminCapability, ViewHoldingSnapshotViaNetworkCapability, ViewHoldingSnapshotViaPurviewCapability)
  val DetailedUploadHoldingSnapshotCapabilities: Set[Capability] = Set(AdminCapability, UploadHoldingSnapshotViaNetworkCapability, UploadHoldingSnapshotViaPurviewCapability)
  val DetailedViewHoldingBasedNotificationCapabilities: Set[Capability] = Set(ViewHoldingBasedLifecycleNotificationCapability)

  /** Defines accepted access keys for snapshot upload. Tasks are not bound to networks, so the keys are generated from Network instead of ProcessingTask */
  val SnapshotAcceptedAccessKeys: AcceptedAccessKeysGenerator[NetworkId] = new AcceptedAccessKeysGenerator[NetworkId] {
    override val capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[NetworkId]] = Map(
      Admin -> acceptedKeyBuilder,
      ViewHoldingSnapshotViaNetwork -> acceptedKeyBuilder.add(networkId => networkId),
      UploadHoldingSnapshotViaNetwork -> acceptedKeyBuilder.add(networkId => networkId),
      ViewHoldingSnapshotViaPurview -> acceptedKeyBuilder.add(networkId => networkId),
      UploadHoldingSnapshotViaPurview -> acceptedKeyBuilder.add(networkId => networkId)
    )
  }

  /** Defines available access keys for snapshot upload */
  val SnapshotAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> availableKeyBuilder,
      ViewHoldingSnapshotViaNetwork -> availableKeyBuilder.add(_.networkId),
      UploadHoldingSnapshotViaNetwork -> availableKeyBuilder.add(_.networkId),
      ViewHoldingSnapshotViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      UploadHoldingSnapshotViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys)
    )
  }

  /** Defines available access keys for holding records */
  val HoldingsAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      ViewHoldingViaNetwork -> availableKeyBuilder.add(_.networkId),
      ViewHoldingViaPurview -> availableKeyBuilder.addList(_.userPurviewIds.map(_.toString)),
      ViewHoldingViaLocation -> availableKeyBuilder.add(_.networkId).addList(_.locations),
      ViewHoldingViaLocationHierarchy -> availableKeyBuilder.add(_.networkId).addList(_.allAccessibleLocations),
      ViewHoldingViaFaNumber -> availableKeyBuilder.add(_.networkId).addList(_.faNumbers),
      ViewHoldingViaCustodianFaNumberCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.custodianFaNumbers.map(cafn => s"${cafn.custodian}:${cafn.id}")),
      ViewHoldingViaDistributorId -> availableKeyBuilder.add(_.networkId).addList(_.distributorId.toList),
      ViewHoldingViaUserExternalId -> AvailableKeyBuilder(buildExternalIdKeys)
    )
  }

  /** Defines available access keys for holding based notification */
  val HoldingsBasedNotificationsAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      ViewHoldingBasedLifecycleNotification -> availableKeyBuilder
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedViewHoldingCapabilities ++
    DetailedViewHoldingSnapshotCapabilities ++
    DetailedUploadHoldingSnapshotCapabilities ++
    DetailedViewHoldingBasedNotificationCapabilities

}