package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.capabilities.utils.CapabilityDefinitionImpl.macroCapability
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

@CapabilityDefinition
object ProductNomenclatureCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "ProductNomenclature"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  final val ViewViaNetwork: String = macroCapability
  final val EditViaNetwork: String = macroCapability

  final val ViewViaNetworkCapability: Capability = Capability(ViewViaNetwork, "", assetClasses)
  final val EditViaNetworkCapability: Capability = Capability(EditViaNetwork, "", assetClasses)

  override val DetailedViewCapabilities: Set[Capability] = Set(ViewViaNetworkCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(EditViaNetworkCapability)

  final val AvailableAccessKeysGenerator: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {

    import com.simonmarkets.capabilities.Capabilities.Admin
    import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._

    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      EditViaNetwork -> AvailableKeyBuilder(buildNetworkKeys)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}