package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.NonRegisteredAnnuityOfferingsCapabilities._
import com.simonmarkets.capabilities.RatesSPOfferingsCapabilities._
import com.simonmarkets.capabilities.RegisteredAnnuityOfferingsCapabilities._
import com.simonmarkets.capabilities.SPOfferingsCapabilities._
import com.simonmarkets.capabilities.StructuredETFOfferingsCapabilities._
import com.simonmarkets.capabilities.AltOfferingCapabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements._
import simon.Id.NetworkId

// TODO add tests
object AllOfferingsCapabilities extends Capabilities with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "AllOfferings"

  /** Defines available access keys for offerings */
  val availableAccessKeysGen = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      /******************************* SP *******************************/
      ViewSPOfferingViaNetworkTypeAndIssuerSymbol -> AvailableKeyBuilder(networkTypeIssuerKeyBuilder),
      EditSPOfferingViaNetworkTypeAndIssuerSymbol -> AvailableKeyBuilder(networkTypeIssuerKeyBuilder),
      TradeSPOfferingViaNetworkTypeAndIssuerSymbol -> AvailableKeyBuilder(networkTypeIssuerKeyBuilder),

      ViewSPOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditSPOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      TradeSPOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApproveSPOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      RejectSPOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      OpenCloseSPOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      StageSPOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      UploadPastOfferingsEntitlementsViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),

      ViewNonRegSSPPastOfferingsViaViewPayoffEntitlements -> AvailableKeyBuilder(payoffEntitlementKeysForAction(_, _)("view")),

      ViewSPOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      TradeSPOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      OverrideTierSPOfferingViaPurviewCapability.name -> AvailableKeyBuilder(buildUserPurviewKeys),
      OverrideTierSPOfferingViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      ViewSPOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      ApproveSPOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      TradeSPOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      EditSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      RejectSPOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      RejectSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      StageSPOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      StageSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      UploadPastOfferingsEntitlementsViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),

      // Key builders below generate both Reg-S and non-Reg-S keys for Reg-S-eligible users.
      ViewUnapprovedSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      TradeUnapprovedSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewApprovedSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),

      ViewStagedSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewRejectedSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),

      TradeSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ApproveSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      OpenCloseSPOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      OpenCloseSPOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      ViewSPOfferingViaUserGuid -> AvailableKeyBuilder(buildGuidKeys),
      TradeSPOfferingViaUserGuid -> AvailableKeyBuilder(buildGuidKeys),
      ViewSPOfferingViaFaNumber -> AvailableKeyBuilder(buildFANumberKeys),
      TradeSPOfferingViaFaNumber -> AvailableKeyBuilder(buildFANumberKeys),
      ViewSPOfferingViaLocation -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      TradeSPOfferingViaLocation -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      /******************************* SP *******************************/

      /******************************* RatesSP *******************************/
      ViewRatesSPOfferingViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditRatesSPOfferingViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
      TradeRatesSPOfferingViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
      RejectRatesSPOfferingViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApproveRatesSPOfferingViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),

      ViewRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability.name -> AvailableKeyBuilder(networkTypeIssuerKeyBuilder),
      EditRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability.name -> AvailableKeyBuilder(networkTypeIssuerKeyBuilder),
      TradeRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability.name -> AvailableKeyBuilder(networkTypeIssuerKeyBuilder),

      ViewRatesSPOfferingViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      ViewApprovedRatesSPOfferingViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      TradeRatesSPOfferingViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      ApproveRatesSPOfferingViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      RejectRatesSPOfferingViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),

      ViewRatesSPOfferingViaNetworkIssuerPurviewCapability.name -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      TradeRatesSPOfferingViaNetworkIssuerPurviewCapability.name -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      ApproveRatesSPOfferingViaNetworkIssuerPurviewCapability.name -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      RejectRatesSPOfferingViaNetworkIssuerPurviewCapability.name -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),

      ViewRatesSPOfferingViaUserGuidCapability.name -> AvailableKeyBuilder(buildGuidKeys),
      TradeRatesSPOfferingViaUserGuidCapability.name -> AvailableKeyBuilder(buildGuidKeys),

      ViewRatesSPOfferingViaFaNumberCapability.name -> AvailableKeyBuilder(buildFANumberKeys),
      TradeRatesSPOfferingViaFaNumberCapability.name -> AvailableKeyBuilder(buildFANumberKeys),

      ViewRatesSPOfferingViaLocationCapability.name -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      TradeRatesSPOfferingViaLocationCapability.name -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),

      OpenCloseRatesSPOfferingViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
      OpenCloseRatesSPOfferingViaPurviewCapability.name -> AvailableKeyBuilder(buildUserPurviewKeys),

      SubmitOrderRatesSPOfferingCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),

      ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability.name -> AvailableKeyBuilder(payoffEntitlementKeysForAction(_, _)("view")),
      /******************************* RatesSP *******************************/

      /******************************* NonRegisteredAnnuity *******************************/
      ViewNonRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditNonRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      TradeNonRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApplyNonRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      IllustrateNonRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApproveNonRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ViewNonRegSNonRegisteredAnnuityPastOffering -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewNonRegisteredAnnuityOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      EditNonRegisteredAnnuityOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      TradeNonRegisteredAnnuityOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      ViewNonRegisteredAnnuityOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      ApproveNonRegisteredAnnuityOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      TradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),

      // Key builders below generate both Reg-S and non-Reg-S keys for Reg-S-eligible users.
      ViewUnapprovedNonRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewApprovedNonRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      TradeNonRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ApproveNonRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ApplyNonRegisteredAnnuityViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      IllustrateNonRegisteredAnnuityViaPayoffEntitlement -> AvailableKeyBuilder(payoffEntitlementKeysForIllustrate),
      CanSellNonRegisteredAnnuityViaPayoffEntitlement -> AvailableKeyBuilder(payoffEntitlementKeysForCanSell),
      ViewNonRegisteredAnnuityOfferingViaUserGuid -> AvailableKeyBuilder(buildGuidKeys),
      TradeNonRegisteredAnnuityOfferingViaUserGuid -> AvailableKeyBuilder(buildGuidKeys),
      ViewNonRegisteredAnnuityOfferingViaFaNumber -> AvailableKeyBuilder(buildFANumberKeys),
      TradeNonRegisteredAnnuityOfferingViaFaNumber -> AvailableKeyBuilder(buildFANumberKeys),
      ViewNonRegisteredAnnuityOfferingViaLocation -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      TradeNonRegisteredAnnuityOfferingViaLocation -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      ViewNonRegisteredAnnuityOfferingViaCusip -> AvailableKeyBuilder(buildCusipKeys),
      TradeNonRegisteredAnnuityOfferingViaCusip -> AvailableKeyBuilder(buildCusipKeys),
      /******************************* NonRegisteredAnnuity *******************************/

      /******************************* RegisteredAnnuity *******************************/
      ViewRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      TradeRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApproveRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApplyRegisteredAnnuityOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ViewNonRegSRegisteredAnnuityPastOffering -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewRegisteredAnnuityOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      EditRegisteredAnnuityOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      TradeRegisteredAnnuityOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      ViewRegisteredAnnuityOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      ApproveRegisteredAnnuityOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      TradeRegisteredAnnuityOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),

      // Key builders below generate both Reg-S and non-Reg-S keys for Reg-S-eligible users.
      ViewUnapprovedRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewApprovedRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      TradeRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ApproveRegisteredAnnuityOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ApplyRegisteredAnnuityViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      IllustrateRegisteredAnnuityViaPayoffEntitlement -> AvailableKeyBuilder(payoffEntitlementKeysForIllustrate),
      CanSellRegisteredAnnuityViaPayoffEntitlement -> AvailableKeyBuilder(payoffEntitlementKeysForCanSell),
      ViewRegisteredAnnuityOfferingViaUserGuid -> AvailableKeyBuilder(buildGuidKeys),
      TradeRegisteredAnnuityOfferingViaUserGuid -> AvailableKeyBuilder(buildGuidKeys),
      ViewRegisteredAnnuityOfferingViaFaNumber -> AvailableKeyBuilder(buildFANumberKeys),
      TradeRegisteredAnnuityOfferingViaFaNumber -> AvailableKeyBuilder(buildFANumberKeys),
      ViewRegisteredAnnuityOfferingViaLocation -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      TradeRegisteredAnnuityOfferingViaLocation -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      ViewRegisteredAnnuityOfferingViaCusip -> AvailableKeyBuilder(buildCusipKeys),
      TradeRegisteredAnnuityOfferingViaCusip -> AvailableKeyBuilder(buildCusipKeys),
      /******************************* RegisteredAnnuity *******************************/

      /******************************* BufferedETF *******************************/
      ViewApprovedStructuredETFOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditApprovedStructuredETFOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApproveStructuredETFOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApproveStructuredETFViaTradePayoffEntitlements -> AvailableKeyBuilder(payoffEntitlementKeysForPayoffTypeAndAction(_, _)("StructuredETF", "trade")),
      ApproveStructuredETFOfferingViaNetworkIssuerPurview -> AvailableKeyBuilder(buildNetworkIssuerPurviewKeys),
      ViewApprovedStructuredETFOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewUnapprovedStructuredETFOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      /******************************* BufferedETF *******************************/

      /******************************* Alternative *******************************/
      ViewAltOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      EditAltOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      ApproveAltOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      TradeAltOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
      OpenCloseAltOfferingViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),

      ViewAltOfferingViaNetworkManager -> AvailableKeyBuilder(buildNetworkKeys),
      ApproveAltOfferingViaNetworkManager -> AvailableKeyBuilder(buildNetworkKeys),

      ViewAltOfferingViaPurviewNetworkManager -> AvailableKeyBuilder(buildUserPurviewKeys),
      ApproveAltOfferingViaPurviewNetworkManager -> AvailableKeyBuilder(buildUserPurviewKeys),

      ViewApprovedAltOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewClosedAltOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewPendingAltOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      EditAltOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ApproveAltOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      TradeAltOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      OpenCloseAltOfferingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),

      ViewApprovedAltOfferingViaNetworkAndViewPayoffEntitlement -> AvailableKeyBuilder(networkAndViewPayoffEntitlementKeyBuilder),
      ViewClosedAltOfferingViaNetworkAndViewPayoffEntitlement -> AvailableKeyBuilder(networkAndViewPayoffEntitlementKeyBuilder),
      ViewPendingAltOfferingViaNetworkAndViewPayoffEntitlement -> AvailableKeyBuilder(networkAndViewPayoffEntitlementKeyBuilder),

      ViewApprovedAltOfferingViaPurviewAndViewPayoffEntitlement -> AvailableKeyBuilder(purviewAndViewPayoffEntitlementKeyBuilder),
      ViewClosedAltOfferingViaPurviewAndViewPayoffEntitlement -> AvailableKeyBuilder(purviewAndViewPayoffEntitlementKeyBuilder),
      ViewPendingAltOfferingViaPurviewAndViewPayoffEntitlement -> AvailableKeyBuilder(purviewAndViewPayoffEntitlementKeyBuilder),

      TradeAltOfferingViaNetworkAndTradePayoffEntitlement -> AvailableKeyBuilder(networkAndTradePayoffEntitlementKeyBuilder),

      ViewApprovedAltOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      ViewClosedAltOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      ViewPendingAltOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      EditAltOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      ApproveAltOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      OpenCloseAltOfferingViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),

      ViewAltOfferingViaProductIssuerNetworkType -> AvailableKeyBuilder(altProductOwnerNetworkTypeKeyBuilder),
      EditAltOfferingViaProductIssuerNetworkType -> AvailableKeyBuilder(altProductOwnerNetworkTypeKeyBuilder),
      ApproveAltOfferingViaProductIssuerNetworkType -> AvailableKeyBuilder(altProductOwnerNetworkTypeKeyBuilder),
      OpenCloseAltOfferingViaProductIssuerNetworkType -> AvailableKeyBuilder(altProductOwnerNetworkTypeKeyBuilder),

      ViewApprovedAltOfferingViaUserCapability.name -> AvailableKeyBuilder(buildGuidKeys),
      ViewPendingAltOfferingViaUserCapability.name -> AvailableKeyBuilder(buildGuidKeys),
      ViewClosedAltOfferingViaUserCapability.name -> AvailableKeyBuilder(buildGuidKeys),
      ViewApprovedAltOfferingViaLocationCapability.name -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      ViewPendingAltOfferingViaLocationCapability.name -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      ViewClosedAltOfferingViaLocationCapability.name -> AvailableKeyBuilder(buildLocationKeysWithoutHierarchy),
      ViewApprovedAltOfferingViaFaNumberCapability.name -> AvailableKeyBuilder(buildFANumberKeys),
      ViewPendingAltOfferingViaFaNumberCapability.name -> AvailableKeyBuilder(buildFANumberKeys),
      ViewClosedAltOfferingViaFaNumberCapability.name -> AvailableKeyBuilder(buildFANumberKeys)
      /******************************* Alternative *******************************/
    )
  }

  private def networkAndViewPayoffEntitlementKeyBuilder(capability: String, user: UserACL): Set[String] = {
    buildNetworkKeys(capability, user).flatMap { networkKey =>
      payoffEntitlementKeysForView(networkKey, user)
    }
  }

  private def purviewAndViewPayoffEntitlementKeyBuilder(capability: String, user: UserACL): Set[String] = {
    buildUserPurviewKeys(capability, user).flatMap { purviewKey =>
      payoffEntitlementKeysForView(purviewKey, user)
    }
  }

  private def networkAndTradePayoffEntitlementKeyBuilder(capability: String, user: UserACL): Set[String] = {
    buildNetworkKeys(capability, user).flatMap { networkKey =>
      payoffEntitlementKeysForTrade(networkKey, user)
    }
  }

  private def networkTypeIssuerKeyBuilder(capability: String, user: UserACL): Set[String] =
    user.networkTypes.toList.flatMap(networkTypes =>
      networkTypes.flatMap(networkType => {
        user.issuerPurviewIds.flatMap(issuerPurviewId => {
          issuerPurviewId.issuers.map(issuer => s"$capability:${networkType.name}:$issuer")
        })
      })).toSet

  private def networkKey(capability: String, userACL: UserACL): String = s"$capability:${userACL.networkId}"

  private def guidKey(capability: String, userACL: UserACL): String = s"$capability:${userACL.userId}"

  private def faNumberKey(capability: String, userACL: UserACL,
      faNumber: String): String = s"$capability:${userACL.networkId}:$faNumber"

  private def locationKey(capability: String, userACL: UserACL,
      location: String): String = s"$capability:${NetworkId.unwrap(userACL.networkId)}:$location"

  private def cusipKey(capability: String, userACL: UserACL,
      cusip: String): String = s"$capability:${NetworkId.unwrap(userACL.networkId)}:$cusip"

  // Give user both non-RegS and RegS keys when they are RegS enabled; only the non-RegS key otherwise. RegS-enabled users should have exactly the same privileges
  // to RegS offerings that they do to non-RegS offerings.
  val regS = "regS"

  private def withRegSKeysIfEligible(keys: Set[String],
      userACL: UserACL): Set[String] = if (!userACL.regSEligible) keys else keys.flatMap(key => Set(key, s"$key:$regS"))

  private def buildNetworkKeys(capability: String,
      userACL: UserACL): Set[String] = withRegSKeysIfEligible(Set(networkKey(capability, userACL)), userACL)

  private def buildGuidKeys(capability: String,
      userACL: UserACL): Set[String] = withRegSKeysIfEligible(Set(guidKey(capability, userACL)), userACL)

  private def buildFANumberKeys(capability: String,
      userACL: UserACL): Set[String] = withRegSKeysIfEligible(userACL.faNumbers.map(faNumber => faNumberKey(capability, userACL, faNumber)), userACL)

  private def buildCusipKeys(capability: String,
      userACL: UserACL): Set[String] = withRegSKeysIfEligible(userACL.cusips.map(cus => cusipKey(capability, userACL, cus)), userACL)

  // Does not take location hierarchy into account.
  // Locations and location hierarchies are used so a firm doesn’t have to pass an entire location list for each user. Instead it’s keyed off of the children location they have access to.
  // It only applies to the lifecycle entities (holdings, accounts, households). We don’t have a firm that uses location hierarchies with offerings or orders. In theory there’s no reason
  // we couldn’t extend them but we don’t have a use-case at this point.
  def buildLocationKeysWithoutHierarchy(capability: String,
      userACL: UserACL): Set[String] = withRegSKeysIfEligible(userACL.locations.map(location => locationKey(capability, userACL, location)), userACL)

  val ApproveCapabilities: Set[String] = SPOfferingsCapabilities.ApproveCapabilities ++ RatesSPOfferingsCapabilities.ApproveCapabilities ++ NonRegisteredAnnuityOfferingsCapabilities.ApproveCapabilities ++ RegisteredAnnuityOfferingsCapabilities.ApproveCapabilities ++ StructuredETFOfferingsCapabilities.ApproveCapabilities ++ AltOfferingCapabilities.ApproveCapabilities
  val TradeCapabilities: Set[String] = SPOfferingsCapabilities.TradeCapabilities ++ RatesSPOfferingsCapabilities.TradeCapabilities ++ NonRegisteredAnnuityOfferingsCapabilities.TradeCapabilities ++ RegisteredAnnuityOfferingsCapabilities.TradeCapabilities ++ ApproveCapabilities ++ AltOfferingCapabilities.TradeCapabilities
  val ApplyCapabilities: Set[String] = NonRegisteredAnnuityOfferingsCapabilities.ApplyCapabilities ++ RegisteredAnnuityOfferingsCapabilities.ApplyCapabilities
  val IllustrateCapabilities: Set[String] = NonRegisteredAnnuityOfferingsCapabilities.IllustrateCapabilities ++ RegisteredAnnuityOfferingsCapabilities.IllustrateCapabilities
  val OpenCloseOfferingCapabilities: Set[String] = SPOfferingsCapabilities.OpenCloseOfferingCapabilities ++ AltOfferingCapabilities.OpenCloseOfferingCapabilities
  val RejectCapabilities: Set[String] = SPOfferingsCapabilities.RejectCapabilities ++ RatesSPOfferingsCapabilities.RejectCapabilities
  val StageCapabilities: Set[String] = SPOfferingsCapabilities.StageCapabilities
  val OverrideTierCapabilities: Set[String] = SPOfferingsCapabilities.OverrideTierCapabilities

  override val DetailedViewCapabilities: Set[Capability] = SPOfferingsCapabilities.DetailedViewCapabilities ++ RatesSPOfferingsCapabilities.DetailedViewCapabilities ++ NonRegisteredAnnuityOfferingsCapabilities.DetailedViewCapabilities ++ RegisteredAnnuityOfferingsCapabilities.DetailedViewCapabilities ++ StructuredETFOfferingsCapabilities.DetailedViewCapabilities ++ AltOfferingCapabilities.DetailedViewCapabilities
  override val DetailedEditCapabilities: Set[Capability] = SPOfferingsCapabilities.DetailedEditCapabilities ++ RatesSPOfferingsCapabilities.DetailedEditCapabilities ++ NonRegisteredAnnuityOfferingsCapabilities.DetailedEditCapabilities ++ RegisteredAnnuityOfferingsCapabilities.DetailedEditCapabilities ++ StructuredETFOfferingsCapabilities.DetailedViewCapabilities ++ AltOfferingCapabilities.DetailedEditCapabilities
  val DetailedApproveCapabilities: Set[Capability] = SPOfferingsCapabilities.DetailedApproveCapabilities ++ RatesSPOfferingsCapabilities.DetailedApproveCapabilities ++ NonRegisteredAnnuityOfferingsCapabilities.DetailedApproveCapabilities ++ RegisteredAnnuityOfferingsCapabilities.DetailedApproveCapabilities ++ StructuredETFOfferingsCapabilities.DetailedApproveCapabilities ++ AltOfferingCapabilities.DetailedApproveCapabilities
  val DetailedTradeCapabilities: Set[Capability] =  DetailedApproveCapabilities ++ SPOfferingsCapabilities.DetailedTradeCapabilities ++ RatesSPOfferingsCapabilities.DetailedTradeCapabilities ++ NonRegisteredAnnuityOfferingsCapabilities.DetailedTradeCapabilities ++ RegisteredAnnuityOfferingsCapabilities.DetailedTradeCapabilities ++ AltOfferingCapabilities.DetailedTradeCapabilities
  val DetailedApplyCapabilities: Set[Capability] = NonRegisteredAnnuityOfferingsCapabilities.DetailedApplyCapabilities ++ RegisteredAnnuityOfferingsCapabilities.DetailedApplyCapabilities
  val DetailedIllustrateCapabilities: Set[Capability] = NonRegisteredAnnuityOfferingsCapabilities.DetailedIllustrateCapabilities ++ RegisteredAnnuityOfferingsCapabilities.DetailedIllustrateCapabilities
  val DetailedOpenCloseOfferingCapabilities: Set[Capability] = SPOfferingsCapabilities.DetailedOpenCloseOfferingCapabilities ++ AltOfferingCapabilities.DetailedOpenCloseOfferingCapabilities
  val DetailedRejectCapabilities: Set[Capability] = SPOfferingsCapabilities.DetailedRejectCapabilities ++ RatesSPOfferingsCapabilities.DetailedRejectCapabilities
  val DetailedStageCapabilities: Set[Capability] = SPOfferingsCapabilities.DetailedStageCapabilities
  val DetailedOverrideTierCapabilities: Set[Capability] = SPOfferingsCapabilities.DetailedOverrideTierCapabilities

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedViewCapabilities ++
    DetailedEditCapabilities ++
    DetailedApproveCapabilities ++
    DetailedTradeCapabilities ++
    DetailedApplyCapabilities ++
    DetailedIllustrateCapabilities ++
    DetailedOpenCloseOfferingCapabilities ++
    DetailedRejectCapabilities ++
    DetailedStageCapabilities ++
    DetailedOverrideTierCapabilities
}
