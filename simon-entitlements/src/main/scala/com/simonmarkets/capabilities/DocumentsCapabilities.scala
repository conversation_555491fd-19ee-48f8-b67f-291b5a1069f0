package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._

object DocumentsCapabilities extends Capabilities {
  override val DomainName = "Documents"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewInvestmentDocumentCapability: Capability = Capability("viewInvestmentDocument", "", assetClasses)
  val EditInvestmentDocumentCapability: Capability = Capability("editInvestmentDocument", "", assetClasses)
  val ViewNetworkDocumentViaNetworkCapability: Capability = Capability("viewNetworkDocumentViaNetwork", "", assetClasses)
  val ViewNetworkDocumentViaPurviewCapability: Capability = Capability("viewNetworkDocumentViaPurview", "", assetClasses)
  val EditNetworkDocumentViaNetworkCapability: Capability = Capability("editNetworkDocumentViaNetwork", "", assetClasses)
  val EditNetworkDocumentViaPurviewCapability: Capability = Capability("editNetworkDocumentViaPurview", "", assetClasses)

  val ViewInvestmentDocument: String = ViewInvestmentDocumentCapability.name
  val EditInvestmentDocument: String = EditInvestmentDocumentCapability.name
  val ViewNetworkDocumentViaNetwork: String = ViewNetworkDocumentViaNetworkCapability.name
  val ViewNetworkDocumentViaPurview: String = ViewNetworkDocumentViaPurviewCapability.name
  val EditNetworkDocumentViaNetwork: String = EditNetworkDocumentViaNetworkCapability.name
  val EditNetworkDocumentViaPurview: String = EditNetworkDocumentViaPurviewCapability.name

  val DocumentAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewInvestmentDocument -> AvailableKeyBuilder(undecoratedKeyBuilder),
      EditInvestmentDocument -> AvailableKeyBuilder(undecoratedKeyBuilder),
      ViewNetworkDocumentViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewNetworkDocumentViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys),
      EditNetworkDocumentViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      EditNetworkDocumentViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys)
    )
  }

  val ViewInvestmentDocumentCapabilities = Set(Admin, ViewInvestmentDocument)
  val EditInvestmentDocumentCapabilities = Set(Admin, EditInvestmentDocument)
  val ViewNetworkDocumentCapabilities = Set(Admin, ViewNetworkDocumentViaNetwork, ViewNetworkDocumentViaPurview)
  val EditNetworkDocumentCapabilities = Set(Admin, EditNetworkDocumentViaNetwork, EditNetworkDocumentViaPurview)

  val DetailedViewInvestmentDocumentCapabilities = Set(AdminCapability, ViewInvestmentDocumentCapability)
  val DetailedEditInvestmentDocumentCapabilities = Set(AdminCapability, EditInvestmentDocumentCapability)
  val DetailedViewNetworkDocumentCapabilities = Set(AdminCapability, ViewNetworkDocumentViaNetworkCapability, ViewNetworkDocumentViaPurviewCapability)
  val DetailedEditNetworkDocumentCapabilities = Set(AdminCapability, EditNetworkDocumentViaNetworkCapability, EditNetworkDocumentViaPurviewCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewInvestmentDocumentCapabilities ++ DetailedEditInvestmentDocumentCapabilities ++ DetailedViewNetworkDocumentCapabilities ++ DetailedEditNetworkDocumentCapabilities
}