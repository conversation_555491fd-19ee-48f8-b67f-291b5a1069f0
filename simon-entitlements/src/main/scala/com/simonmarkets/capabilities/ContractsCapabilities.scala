package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.capabilities.utils.CapabilityDefinitionImpl.macroCapability
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

@CapabilityDefinition
object ContractsCapabilities extends Capabilities {
  override val DomainName: String = "Contracts"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val LockContractViaEditPayoff: String = macroCapability
  val UnlockContractViaEditPayoff: String = macroCapability

  val LockContractViaEditPayoffCapability: Capability = Capability(LockContractViaEditPayoff, "", assetClasses)
  val UnlockContractViaEditPayoffCapability: Capability = Capability(UnlockContractViaEditPayoff, "", assetClasses)

  val lockContractViaEditPayoffEntitlements = Set(Admin, LockContractViaEditPayoff)
  val unlockContractViaEditPayoffEntitlements = Set(Admin, UnlockContractViaEditPayoff)
  val DetailedlockContractViaEditPayoffEntitlements = Set(AdminCapability, LockContractViaEditPayoffCapability)
  val DetailedunlockContractViaEditPayoffEntitlements = Set(AdminCapability, UnlockContractViaEditPayoffCapability)

  val ContractsAvailableAccessKeysGenerator: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {

    import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
    import com.simonmarkets.capabilities.Capabilities.Admin

    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      LockContractViaEditPayoff -> AvailableKeyBuilder(undecoratedKeyBuilder),
      UnlockContractViaEditPayoff -> AvailableKeyBuilder(undecoratedKeyBuilder)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedlockContractViaEditPayoffEntitlements ++ DetailedunlockContractViaEditPayoffEntitlements
}
