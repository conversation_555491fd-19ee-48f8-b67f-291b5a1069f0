package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}


object CryptoPersonsCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "CryptoPersons"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.DigitalAssetClass))

  val ViewCryptoPersonViaNetwork: String = "viewCryptoPersonViaNetwork"
  val EditCryptoPersonViaNetwork: String = "editCryptoPersonViaNetwork"

  val ViewCryptoPersonViaNetworkCapability: Capability = Capability(ViewCryptoPersonViaNetwork,
    "Allows one to view a CryptoPerson if one's Network Id matches the CryptoPerson's Network Id", assetClasses)
  val EditCryptoPersonViaNetworkCapability: Capability = Capability(EditCryptoPersonViaNetwork,
    "Allows one to edit a CryptoPerson if one's Network Id matches the CryptoPerson's Network Id", assetClasses)

  override val ViewCapabilities: Set[String] = Set(Admin, ViewCryptoPersonViaNetwork)
  override val EditCapabilities: Set[String] = Set(Admin, EditCryptoPersonViaNetwork)

  override def DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewCryptoPersonViaNetworkCapability)

  override def DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditCryptoPersonViaNetworkCapability)

  override def toSet: Set[String] = ViewCapabilities ++ EditCapabilities

  def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewCryptoPersonViaNetwork -> availableKeyBuilder.add(_.networkId),
    EditCryptoPersonViaNetwork -> availableKeyBuilder.add(_.networkId),
  )
}