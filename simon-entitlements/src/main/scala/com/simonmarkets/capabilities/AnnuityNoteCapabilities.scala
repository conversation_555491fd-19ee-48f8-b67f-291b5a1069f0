package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGuidKeys, buildNetworkKeys}
import com.simonmarkets.entitlements._

object AnnuityNoteCapabilities
  extends Capabilities
    with HasViewCapabilities
    with HasDetailedViewCapabilities
    with HasEditCapabilities
    with HasDetailedEditCapabilities
    with AvailableAccessKeysGenerator {

  override val DomainName = "AnnuityNote"
  val assetClasses: Option[Seq[String]] = Some(Seq(AssetClasses.AnnuitiesAssetClass))

  val ViewAnnuityNoteViaUserCreatedCapability: Capability =
    Capability("viewAnnuityNoteViaUserCreated", "", assetClasses)
  val EditAnnuityNoteViaUserCreatedCapability: Capability =
    Capability("editAnnuityNoteViaUserCreated", "", assetClasses)
  val ViewAnnuityNoteViaNetworkCapability: Capability =
    Capability("viewAnnuityNoteViaNetwork", "", assetClasses)


  val ViewAnnuityNoteViaUserCreated: String = ViewAnnuityNoteViaUserCreatedCapability.name
  val ViewAnnuityNoteViaNetwork: String = ViewAnnuityNoteViaNetworkCapability.name
  val EditAnnuityNoteViaUserCreated: String = EditAnnuityNoteViaUserCreatedCapability.name

  override def DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ReadOnlyAdminCapability,
    ViewAnnuityNoteViaUserCreatedCapability,
    ViewAnnuityNoteViaNetworkCapability,
  )

  override def DetailedEditCapabilities: Set[Capability] = Set(
    EditAnnuityNoteViaUserCreatedCapability,
    AdminCapability,
  )

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override val ViewCapabilities: Set[String] = DetailedViewCapabilities.map(_.name)
  override val EditCapabilities: Set[String] = DetailedEditCapabilities.map(_.name)


  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ReadOnlyAdmin -> AvailableKeyBuilder(buildAdminKeys),
    ViewAnnuityNoteViaUserCreated -> AvailableKeyBuilder(buildGuidKeys),
    ViewAnnuityNoteViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditAnnuityNoteViaUserCreated -> AvailableKeyBuilder(buildGuidKeys),
  )
}
