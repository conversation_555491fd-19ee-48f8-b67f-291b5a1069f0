package com.simonmarkets.capabilities


import com.goldmansachs.marquee.pipg.UserRole.Issuer
import com.goldmansachs.marquee.pipg.{GroupIdType, License, UserACL}
import com.simonmarkets.capabilities.Capabilities._
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGroupIdKeys}
import com.simonmarkets.entitlements._
import simon.Id._

object AnnuityHoldingsCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "AnnuityHoldings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AnnuitiesAssetClass))

  val viewAnnuityHoldingsViaNetworkCapability: Capability = Capability("viewAnnuityHoldingsViaNetwork", "Allows a user to view all Annuity Holdings in their network", assetClasses)
  val viewAnnuityHoldingsViaIssuerCapability: Capability = Capability("viewAnnuityHoldingsViaIssuer", "", assetClasses)
  val viewAnnuityHoldingsViaLicenseCapability: Capability = Capability("viewAnnuityHoldingsViaLicense", "", assetClasses)
  val viewAnnuityHoldingsViaPurviewCapability: Capability = Capability("viewAnnuityHoldingsViaPurview", "", assetClasses)
  val viewAnnuityHoldingsViaFaNumberCapability: Capability = Capability("viewAnnuityHoldingsViaFaNumber", "", assetClasses)
  val viewAnnuityHoldingsViaLocationCapability: Capability = Capability("viewAnnuityHoldingsViaLocation", "", assetClasses)
  val viewAnnuityHoldingsViaFaNumberWithPurviewCapability: Capability = Capability("viewAnnuityHoldingsViaFaNumberWithPurview", "Allows an advisor to view holdings in another network over which it has purview and corresponding fa numbers", assetClasses)
  val viewAnnuityHoldingsViaLicenseWithPurviewCapability: Capability = Capability("viewAnnuityHoldingsViaLicenseWithPurview", "Allows an advisor to view holdings in another network over which it has purview and corresponding licenses", assetClasses)
  val editAnnuityHoldingsViaNetworkCapability: Capability = Capability("editAnnuityHoldingsViaNetwork", "", assetClasses)
  val viewAnnuityHoldingsViaPurviewLicensesCapability: Capability = Capability(
    name = "viewAnnuityHoldingsViaPurviewLicenses",
    description = "Allow advisor to view holdings where the holding on the license instersects with their purviewLicenses array",
    assetClass = assetClasses
  )
  val viewHoldingsViaDistributorNSCCCapability: Capability = Capability(
    name = "viewHoldingsViaDistributorNSCC",
    description = "Allow advisor to view holdings where the distributor nscc code on the  holding  instersects with their purviewNsccCodes array",
    assetClass = assetClasses
  )
  val viewAnnuityHoldingsViaDistributorIdGroupCapability: Capability = Capability(
    name = "viewAnnuityHoldingsViaDistributorIdGroup",
    description = "Allow advisor to view holdings where the distributor assigned id intersects their (...)",
    assetClass = assetClasses
  )

  //view
  val viewAnnuityHoldingsViaNetwork: String = viewAnnuityHoldingsViaNetworkCapability.name
  val viewAnnuityHoldingsViaIssuer: String = viewAnnuityHoldingsViaIssuerCapability.name
  val viewAnnuityHoldingsViaLicense: String = viewAnnuityHoldingsViaLicenseCapability.name
  val viewAnnuityHoldingsViaPurview: String = viewAnnuityHoldingsViaPurviewCapability.name
  val viewAnnuityHoldingsViaFaNumber: String = viewAnnuityHoldingsViaFaNumberCapability.name
  val viewAnnuityHoldingsViaLocation: String = viewAnnuityHoldingsViaLocationCapability.name
  val viewAnnuityHoldingsViaFaNumberWithPurview: String = viewAnnuityHoldingsViaFaNumberWithPurviewCapability.name
  val viewAnnuityHoldingsViaLicenseWithPurview: String = viewAnnuityHoldingsViaLicenseWithPurviewCapability.name
  val viewHoldingsViaDistributorNSCCC: String = viewHoldingsViaDistributorNSCCCapability.name
  val viewAnnuityHoldingsViaDistributorIdGroup:String = viewAnnuityHoldingsViaDistributorIdGroupCapability.name

  //edit
  val editAnnuityHoldingsViaNetwork: String = editAnnuityHoldingsViaNetworkCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    viewAnnuityHoldingsViaNetworkCapability,
    viewAnnuityHoldingsViaIssuerCapability,
    viewAnnuityHoldingsViaLicenseCapability,
    viewAnnuityHoldingsViaPurviewCapability,
    viewAnnuityHoldingsViaFaNumberCapability,
    viewAnnuityHoldingsViaLocationCapability,
    viewAnnuityHoldingsViaFaNumberWithPurviewCapability,
    viewAnnuityHoldingsViaLicenseWithPurviewCapability,
    viewAnnuityHoldingsViaPurviewLicensesCapability,
    viewHoldingsViaDistributorNSCCCapability,
    viewAnnuityHoldingsViaDistributorIdGroupCapability
  )
  override val DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, editAnnuityHoldingsViaNetworkCapability)
  override val ViewCapabilities: Set[String] = DetailedViewCapabilities.map(_.name)
  override val EditCapabilities: Set[String] = DetailedEditCapabilities.map(_.name)

  private val generateIssuerKey = (u: UserACL) => u.roles.filter(_ == Issuer).map(_ => u.networkId.toString)
  private val serializeLicense = (l: License) => s"${l.name.name}:${l.number}"

  val AnnuityHoldingsAvailableAccessKeys: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      viewAnnuityHoldingsViaNetwork -> availableKeyBuilder.add(_.networkId),
      viewAnnuityHoldingsViaIssuer -> availableKeyBuilder.addList(generateIssuerKey),
      viewAnnuityHoldingsViaLicense -> availableKeyBuilder.add(_.networkId).addList(_.licenses.map(serializeLicense)),
      viewAnnuityHoldingsViaPurview -> availableKeyBuilder.addList(_.userPurviewIds.map(_.toString)),
      viewAnnuityHoldingsViaFaNumber -> availableKeyBuilder.add(_.networkId).addList(_.faNumbers),
      viewAnnuityHoldingsViaLocation -> availableKeyBuilder.add(_.networkId).addList(_.locations),
      viewAnnuityHoldingsViaFaNumberWithPurview -> availableKeyBuilder.addList(_.userPurviewIds.map(_.toString)).addList(_.faNumbers),
      viewAnnuityHoldingsViaLicenseWithPurview -> availableKeyBuilder.addList(_.userPurviewIds.map(_.toString)).addList(_.licenses.map(serializeLicense)),
      viewAnnuityHoldingsViaPurviewLicensesCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.purviewLicenses.getOrElse(Set.empty).map(serializeLicense)),
      viewHoldingsViaDistributorNSCCCapability.name -> availableKeyBuilder.add(_.networkId).addList(_.purviewNsccCodes.getOrElse(Set.empty)),
      viewAnnuityHoldingsViaDistributorIdGroup -> AvailableKeyBuilder(buildGroupIdKeys(GroupIdType.DistributorId.productPrefix)),
      editAnnuityHoldingsViaNetwork -> availableKeyBuilder.add(_.networkId),
    )
  }

  val QueryAnnuityHoldingsAcceptedAccessKeysGenerator: AcceptedAccessKeysGenerator[AnnuityHoldingsResource] = new AcceptedAccessKeysGenerator[AnnuityHoldingsResource] {
    override val capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[AnnuityHoldingsResource]] = Map(
      Admin -> acceptedKeyBuilder,
      viewAnnuityHoldingsViaNetwork -> acceptedKeyBuilder.add(_.distributorNetworkId),
      viewAnnuityHoldingsViaIssuer -> acceptedKeyBuilder.addList(_.carrierNetworkId.toList.map(_.toString)),
      viewAnnuityHoldingsViaLicense -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).addList(_.licenses.map(serializeLicense)),
      viewAnnuityHoldingsViaPurview -> acceptedKeyBuilder.add(_.distributorNetworkId),
      viewAnnuityHoldingsViaFaNumber -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).addList(_.faNumbers),
      viewAnnuityHoldingsViaLocation -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).addList(_.locations),
      viewAnnuityHoldingsViaFaNumberWithPurview -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).addList(_.faNumbers),
      viewAnnuityHoldingsViaLicenseWithPurview -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).addList(_.licenses.map(serializeLicense)),
      viewAnnuityHoldingsViaPurviewLicensesCapability.name -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).addList(_.licenses.map(serializeLicense)),
      viewHoldingsViaDistributorNSCCCapability.name -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).add(_.distributorNSCC),
      viewAnnuityHoldingsViaDistributorIdGroup -> acceptedKeyBuilder[AnnuityHoldingsResource].add(_.distributorNetworkId).addList(_.distributorAssignedIds)
    )
  }
  val UpdateAnnuityHoldingsAcceptedAccessKeysGenerator: AcceptedAccessKeysGenerator[NetworkId] = new AcceptedAccessKeysGenerator[NetworkId] {
    override val capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[NetworkId]] = Map(
      Admin -> acceptedKeyBuilder,
      editAnnuityHoldingsViaNetwork -> acceptedKeyBuilder.add(networkId => networkId)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  trait AnnuityHoldingsResource {
    def carrierNetworkId: Option[NetworkId]

    def distributorNetworkId: NetworkId

    def distributorNSCC: String

    def licenses: Set[License]

    def faNumbers: Set[String]

    def distributorAssignedIds: Set[String]

    def locations: Set[String]
  }
}
