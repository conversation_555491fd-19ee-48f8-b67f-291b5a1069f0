package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.{HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object NonRegisteredAnnuityOfferingsCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName = "NonRegisteredAnnuityOfferings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AnnuitiesAssetClass))

  // NonRegisteredAnnuity issuers should have these capabilities. Note that NetworkType.Issuer applies to product originators of all asset classes distributed on SIMON.
  val ViewNonRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("viewNonRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditNonRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("editNonRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val TradeNonRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ApproveNonRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("approveNonRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ApplyNonRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("applyNonRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val IllustrateNonRegisteredAnnuityOfferingViaNetworkTypeCapability: Capability = Capability("illustrateNonRegisteredAnnuityOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewNonRegisteredAnnuityOfferingViaNetworkType: String = ViewNonRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val EditNonRegisteredAnnuityOfferingViaNetworkType: String = EditNonRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val TradeNonRegisteredAnnuityOfferingViaNetworkType: String = TradeNonRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val ApproveNonRegisteredAnnuityOfferingViaNetworkType: String = ApproveNonRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val IllustrateNonRegisteredAnnuityOfferingViaNetworkType: String = IllustrateNonRegisteredAnnuityOfferingViaNetworkTypeCapability.name
  val ApplyNonRegisteredAnnuityOfferingViaNetworkType: String = ApplyNonRegisteredAnnuityOfferingViaNetworkTypeCapability.name

  // For NonRegisteredAnnuity FA, FA Manager, Wholesalers
  val ViewNonRegSNonRegisteredAnnuityPastOfferingCapability: Capability = Capability("viewNonRegSNonRegisteredAnnuityPastOffering", "", assetClasses)
  val ViewNonRegSNonRegisteredAnnuityPastOffering: String = ViewNonRegSNonRegisteredAnnuityPastOfferingCapability.name

  // userACL.userPurviewIds contains a networkId this offering is offeredTo
  // Wholesaler
  val ViewNonRegisteredAnnuityOfferingViaPurviewCapability: Capability = Capability("viewNonRegisteredAnnuityOfferingViaPurview", "", assetClasses)
  val TradeNonRegisteredAnnuityOfferingViaPurviewCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaPurview", "", assetClasses)
  val EditNonRegisteredAnnuityOfferingViaPurviewCapability: Capability = Capability("editNonRegisteredAnnuityOfferingViaPurview", "", assetClasses)
  val ViewNonRegisteredAnnuityOfferingViaPurview: String = ViewNonRegisteredAnnuityOfferingViaPurviewCapability.name
  val TradeNonRegisteredAnnuityOfferingViaPurview: String = TradeNonRegisteredAnnuityOfferingViaPurviewCapability.name
  val EditNonRegisteredAnnuityOfferingViaPurview: String = EditNonRegisteredAnnuityOfferingViaPurviewCapability.name

  // userACL.issuerPurviewIds contains a (networkId, issuerSymbol) pair that matches the pair (a networkId in offering.offeredTo, offering.issuerSymbol)
  val ViewNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("viewNonRegisteredAnnuityOfferingViaNetworkIssuerPurview", "", assetClasses) // FA Manager, Issuer, Wholesaler
  val TradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurview", "", assetClasses) // FA Manager, Issuer, Wholesaler
  val ApproveNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("approveNonRegisteredAnnuityOfferingViaNetworkIssuerPurview", "", assetClasses) // FA Manager
  val ViewNonRegisteredAnnuityOfferingViaNetworkIssuerPurview: String = ViewNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability.name
  val TradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurview: String = TradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability.name
  val ApproveNonRegisteredAnnuityOfferingViaNetworkIssuerPurview: String = ApproveNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability.name

  // All 4 capabilities below, as well as the user-guid, FA Number, and Location capabilities following them, apply to Reg-S offerings only if userACL.regSEligible is true.
  // If userACL.networkId matches a networkId this offering is offeredTo, and view/trade/approve action is specified in legacy FA OR FAM roles' actions.
  val ViewUnapprovedNonRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("viewUnapprovedNonRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA Manager
  val ViewApprovedNonRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("viewApprovedNonRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA, FA Manager
  val TradeNonRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA, FA Manager. Note that unlike in the legacy roles-based approach, now if FA Managers can trade an offering, FAs will be able to do so as well; i.e. there is no separate "tradeUnapproved" capability.
  val ApproveNonRegisteredAnnuityOfferingViaNetworkCapability: Capability = Capability("approveNonRegisteredAnnuityOfferingViaNetwork", "", assetClasses) // FA Manager
  val ApplyNonRegisteredAnnuityViaNetworkCapability: Capability = Capability("applyNonRegisteredAnnuityViaNetwork", "Allows annuity IOI and application submission for non registered products", assetClasses) //launch annuity application for networks with integrated e-app
  val IllustrateNonRegisteredAnnuityViaPayoffEntitlementCapability: Capability = Capability("illustrateNonRegisteredAnnuityViaPayoffEntitlement", "", assetClasses) //illustrate annuity for products/networks with integrated illustration
  val CanSellNonRegisteredAnnuityViaPayoffEntitlementCapability: Capability = Capability("canSellNonRegisteredAnnuityViaPayoffEntitlement", "", assetClasses)
  val ViewUnapprovedNonRegisteredAnnuityOfferingViaNetwork: String = ViewUnapprovedNonRegisteredAnnuityOfferingViaNetworkCapability.name
  val ViewApprovedNonRegisteredAnnuityOfferingViaNetwork: String = ViewApprovedNonRegisteredAnnuityOfferingViaNetworkCapability.name
  val TradeNonRegisteredAnnuityOfferingViaNetwork: String = TradeNonRegisteredAnnuityOfferingViaNetworkCapability.name
  val ApproveNonRegisteredAnnuityOfferingViaNetwork: String = ApproveNonRegisteredAnnuityOfferingViaNetworkCapability.name
  val ApplyNonRegisteredAnnuityViaNetwork: String = ApplyNonRegisteredAnnuityViaNetworkCapability.name
  val IllustrateNonRegisteredAnnuityViaPayoffEntitlement: String = IllustrateNonRegisteredAnnuityViaPayoffEntitlementCapability.name
  val CanSellNonRegisteredAnnuityViaPayoffEntitlement = CanSellNonRegisteredAnnuityViaPayoffEntitlementCapability.name

  // userACL.userId matches an userId in offeredTo.users, with action view/trade specified respectively
  val ViewNonRegisteredAnnuityOfferingViaUserGuidCapability: Capability = Capability("viewNonRegisteredAnnuityOfferingViaUserGuid", "", assetClasses)
  val TradeNonRegisteredAnnuityOfferingViaUserGuidCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaUserGuid", "", assetClasses)
  val ViewNonRegisteredAnnuityOfferingViaUserGuid: String = ViewNonRegisteredAnnuityOfferingViaUserGuidCapability.name
  val TradeNonRegisteredAnnuityOfferingViaUserGuid: String = TradeNonRegisteredAnnuityOfferingViaUserGuidCapability.name

  // userACL.faNumbers contains an faNumber that matches one item in offeredTo.faNumbers.faNumber (scoped by offeredTo.networkId), with view/trade action specified respectively
  val ViewNonRegisteredAnnuityOfferingViaFaNumberCapability: Capability = Capability("viewNonRegisteredAnnuityOfferingViaFaNumber", "", assetClasses)
  val TradeNonRegisteredAnnuityOfferingViaFaNumberCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaFaNumber", "", assetClasses)
  val ViewNonRegisteredAnnuityOfferingViaFaNumber: String = ViewNonRegisteredAnnuityOfferingViaFaNumberCapability.name
  val TradeNonRegisteredAnnuityOfferingViaFaNumber: String = TradeNonRegisteredAnnuityOfferingViaFaNumberCapability.name

  // userACL.locations contains a location (scoped by networkId of user) that matches an item in offeredTo.locations.location (scoped by offeredTo.networkId), with view/trade action specified respectively
  val ViewNonRegisteredAnnuityOfferingViaLocationCapability: Capability = Capability("viewNonRegisteredAnnuityOfferingViaLocation", "", assetClasses)
  val TradeNonRegisteredAnnuityOfferingViaLocationCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaLocation", "", assetClasses)
  val ViewNonRegisteredAnnuityOfferingViaLocation: String = ViewNonRegisteredAnnuityOfferingViaLocationCapability.name
  val TradeNonRegisteredAnnuityOfferingViaLocation: String = TradeNonRegisteredAnnuityOfferingViaLocationCapability.name

  // userACL.cusips contains the CUSIPs that mathes item in offered.network for this CUSIP
  val ViewNonRegisteredAnnuityOfferingViaCusipCapability: Capability = Capability("viewNonRegisteredAnnuityOfferingViaCusip", "", assetClasses)
  val TradeNonRegisteredAnnuityOfferingViaCusipCapability: Capability = Capability("tradeNonRegisteredAnnuityOfferingViaCusip", "", assetClasses)
  val ViewNonRegisteredAnnuityOfferingViaCusip: String = ViewNonRegisteredAnnuityOfferingViaCusipCapability.name
  val TradeNonRegisteredAnnuityOfferingViaCusip: String = TradeNonRegisteredAnnuityOfferingViaCusipCapability.name

  val ApproveCapabilities = Set(Admin, ApproveNonRegisteredAnnuityOfferingViaNetwork, ApproveNonRegisteredAnnuityOfferingViaNetworkIssuerPurview, ApproveNonRegisteredAnnuityOfferingViaNetworkType)
  val TradeCapabilities = Set(Admin, TradeNonRegisteredAnnuityOfferingViaNetworkType, TradeNonRegisteredAnnuityOfferingViaNetwork, TradeNonRegisteredAnnuityOfferingViaPurview, TradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurview, TradeNonRegisteredAnnuityOfferingViaUserGuid, TradeNonRegisteredAnnuityOfferingViaFaNumber, TradeNonRegisteredAnnuityOfferingViaLocation, TradeNonRegisteredAnnuityOfferingViaCusip)
  val ApplyCapabilities = Set(Admin, ApplyNonRegisteredAnnuityViaNetwork, ApplyNonRegisteredAnnuityOfferingViaNetworkType)
  val IllustrateCapabilities = Set(Admin, IllustrateNonRegisteredAnnuityViaPayoffEntitlement, IllustrateNonRegisteredAnnuityOfferingViaNetworkType)

  override val DetailedViewCapabilities = Set(AdminCapability, ViewNonRegisteredAnnuityOfferingViaNetworkTypeCapability, ViewNonRegSNonRegisteredAnnuityPastOfferingCapability, ViewUnapprovedNonRegisteredAnnuityOfferingViaNetworkCapability, ViewApprovedNonRegisteredAnnuityOfferingViaNetworkCapability, ViewNonRegisteredAnnuityOfferingViaPurviewCapability, ViewNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability, ViewNonRegisteredAnnuityOfferingViaUserGuidCapability, ViewNonRegisteredAnnuityOfferingViaFaNumberCapability, ViewNonRegisteredAnnuityOfferingViaLocationCapability, ViewNonRegisteredAnnuityOfferingViaCusipCapability)
  override val DetailedEditCapabilities = Set(EditNonRegisteredAnnuityOfferingViaNetworkTypeCapability, EditNonRegisteredAnnuityOfferingViaPurviewCapability, AdminCapability)
  val DetailedApproveCapabilities = Set(AdminCapability, ApproveNonRegisteredAnnuityOfferingViaNetworkCapability, ApproveNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability, ApproveNonRegisteredAnnuityOfferingViaNetworkTypeCapability)
  val DetailedTradeCapabilities = Set(AdminCapability, TradeNonRegisteredAnnuityOfferingViaNetworkTypeCapability, TradeNonRegisteredAnnuityOfferingViaNetworkCapability, TradeNonRegisteredAnnuityOfferingViaPurviewCapability, TradeNonRegisteredAnnuityOfferingViaNetworkIssuerPurviewCapability, TradeNonRegisteredAnnuityOfferingViaUserGuidCapability, TradeNonRegisteredAnnuityOfferingViaFaNumberCapability, TradeNonRegisteredAnnuityOfferingViaLocationCapability, TradeNonRegisteredAnnuityOfferingViaCusipCapability)
  val DetailedApplyCapabilities = Set(AdminCapability, ApplyNonRegisteredAnnuityViaNetworkCapability, ApplyNonRegisteredAnnuityOfferingViaNetworkTypeCapability, ApplyNonRegisteredAnnuityOfferingViaNetworkTypeCapability)
  val DetailedIllustrateCapabilities = Set(AdminCapability, IllustrateNonRegisteredAnnuityViaPayoffEntitlementCapability, IllustrateNonRegisteredAnnuityOfferingViaNetworkTypeCapability)
  val DetailedCanSellCapabilities = Set(AdminCapability, CanSellNonRegisteredAnnuityViaPayoffEntitlementCapability)

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedViewCapabilities ++
      DetailedEditCapabilities ++
      DetailedApproveCapabilities ++
      DetailedTradeCapabilities ++
      DetailedApplyCapabilities ++
      DetailedIllustrateCapabilities ++
      DetailedCanSellCapabilities
}
