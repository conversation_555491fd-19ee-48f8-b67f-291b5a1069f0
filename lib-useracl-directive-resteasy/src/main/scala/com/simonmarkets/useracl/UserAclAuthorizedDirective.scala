package com.simonmarkets.useracl

import akka.actor.ActorSystem
import akka.http.scaladsl.server.Directives.{ onComplete, provide, reject }
import akka.http.scaladsl.server.{ AuthorizationFailedRejection, Directive1 }
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.logging.{ TraceId, TraceLogging }
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.requests.AuthorizedDirectives

import scala.concurrent.{ ExecutionContext, Future }
import scala.util.{ Failure, Success }

case class UserAclAuthorizedDirective(
  userAclSupplier: User => Set[String] => TraceId => Directive1[UserACL]
) extends AuthorizedDirectives.Impl[UserACL](userAclSupplier)

object UserAclAuthorizedDirective extends TraceLogging {

  final def apply(
    aclClientConfig: AclClientConfig
  )(implicit
    ec: ExecutionContext,
    as: ActorSystem,
    mat: Materializer
  ): UserAclAuthorizedDirective = UserAclAuthorizedDirective(HttpACLClient(aclClientConfig))

  final def apply(aclClient: HttpACLClient): UserAclAuthorizedDirective = {
    val supplier: User => TraceId => Future[UserACL] = user => tId => aclClient.getUserACL(user.userId)(tId)
    UserAclAuthorizedDirective(supplier)
  }

  final def apply(aclSupplier: => User => TraceId => Future[UserACL]): UserAclAuthorizedDirective = {
    val supplier: User => Set[String] => TraceId => Directive1[UserACL] =
      user =>
        capabilities =>
          trace => {
            implicit val traceId: TraceId = trace
            toAuthorizedUserACL(user, aclSupplier(user)(trace))(capabilities)
          }

    UserAclAuthorizedDirective(supplier)
  }

  private def toAuthorizedUserACL(
    user: User,
    fUserAcl: Future[UserACL]
  )(
    capabilities: Set[String]
  )(implicit traceId: TraceId): Directive1[UserACL] =
    toUserACL(user, fUserAcl).flatMap { acl =>
      if (capabilities.isEmpty || acl.capabilities.exists(capabilities.contains)) {
        provide(acl)
      } else {
        log.warn(
          s"Authorization error: User has none of the required capabilities",
          "userId" -> user.userId,
          capabilities
        )
        reject(AuthorizationFailedRejection)
      }
    }

  private def toUserACL(user: User, fAcl: Future[UserACL])(implicit traceId: TraceId): Directive1[UserACL] =
    onComplete(fAcl).flatMap {
      case Success(userACL) => provide(userACL)
      case Failure(exception) =>
        log.warn("Authorization error: Unable to lookup user", user.userId, exception.getMessage)
        reject(AuthorizationFailedRejection)
    }

}
