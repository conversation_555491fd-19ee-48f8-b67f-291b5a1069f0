package com.simonmarkets.networks

import io.simon.openapi.annotation.Field.Ref

case class ExternalId(
  @Ref(NetworksDefinitions.StringWithWhiteSpace128LengthFull) subject: String,
  @Ref(NetworksDefinitions.ExternalId) id: String
)

object ExternalId {
  def fromStringMap(map: Map[String, String]): Seq[ExternalId] = map.map { case (k, v) => ExternalId(k, v) }.toSeq

  def toStringMap(seq: Seq[ExternalId]): Map[String, String] = seq.map(id => (id.subject, id.id)).toMap

  def upsert(existing: Seq[ExternalId], additional: Seq[ExternalId]): Seq[ExternalId] = {
    val newSubjects = additional.map(_.subject).toSet
    existing.filterNot(id => newSubjects.contains(id.subject)) ++ additional
  }
}
