package com.simonmarkets.networks.enums

import org.scalatest.{Matchers, WordSpec}


class DomicileCodeSpec extends WordSpec with Matchers {
  "DomicileCode" should {
    "handle case-insensitive lookup" in {
      DomicileCode("us") shouldBe DomicileCode.US
      DomicileCode("Ca") shouldBe DomicileCode.CA
    }

    "EnumNotFound for invalid country codes" in {
      DomicileCode("123") shouldBe DomicileCode.EnumNotFound
      DomicileCode("") shouldBe DomicileCode.EnumNotFound
    }

    "have proper string representation" in {
      DomicileCode.US.toString shouldBe "US"
      DomicileCode.CA.toString shouldBe "CA"
    }

    "be comparable" in {
      DomicileCode.US shouldEqual DomicileCode.US
      DomicileCode.US should not equal DomicileCode.CA
    }
  }
}
