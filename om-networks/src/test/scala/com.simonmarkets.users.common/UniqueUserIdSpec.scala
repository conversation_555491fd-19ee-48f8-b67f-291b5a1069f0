package com.simonmarkets.users.common

import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.common.UniqueUserId._
import org.scalatest.{Matchers, WordSpec}
import simon.Id.NetworkId


class UniqueUserIdSpec extends WordSpec with Matchers {

  "unsafeApply" when {
    "Oms" should {
      "be left when missing id value" in {
        UniqueUserId.unsafeApply(IdType.Oms, None, None, None).isLeft shouldBe true
      }

      "be right when has id value" in {
        UniqueUserId.unsafeApply(IdType.Oms, Some("oms"), None, None) shouldBe Right(Oms("oms"))
      }
    }

    "Distributor" should {
      "be left when missing id value or network" in {
        UniqueUserId.unsafeApply(IdType.Distributor, None, None, None).isLeft shouldBe true
        UniqueUserId.unsafeApply(IdType.Distributor, Some("distId"), None, None).isLeft shouldBe true
        UniqueUserId.unsafeApply(IdType.Distributor, None, None, Some(NetworkId("netId"))).isLeft shouldBe true
      }

      "be right when has id value and network" in {
        UniqueUserId.unsafeApply(IdType.Distributor, Some("distId"), None, Some(NetworkId("netId"))) shouldBe
          Right(Distributor("distId", NetworkId("netId")))
      }
    }

    "UserId" should {
      "be left when missing id value" in {
        UniqueUserId.unsafeApply(IdType.UserId, None, None, None).isLeft shouldBe true
      }

      "be right when has id value" in {
        UniqueUserId.unsafeApply(IdType.UserId, Some("guid"), None, None) shouldBe Right(Guid("guid"))
      }
    }

    "NPN" should {
      "be left when missing id value or network" in {
        UniqueUserId.unsafeApply(IdType.NPN, None, None, None).isLeft shouldBe true
        UniqueUserId.unsafeApply(IdType.NPN, Some("npn"), None, None).isLeft shouldBe true
        UniqueUserId.unsafeApply(IdType.NPN, None, None, Some(NetworkId("netId"))).isLeft shouldBe true
      }

      "be right when has id value and network" in {
        UniqueUserId.unsafeApply(IdType.NPN, Some("npn"), None, Some(NetworkId("netId"))) shouldBe
          Right(NPN("npn", NetworkId("netId")))
      }
    }

    "Email" should {
      "be left when missing id value or network" in {
        UniqueUserId.unsafeApply(IdType.Email, None, None, None).isLeft shouldBe true
        UniqueUserId.unsafeApply(IdType.Email, Some("email"), None, None).isLeft shouldBe true
        UniqueUserId.unsafeApply(IdType.Email, None, None, Some(NetworkId("netId"))).isLeft shouldBe true
      }

      "be right when has id value and network" in {
        UniqueUserId.unsafeApply(IdType.Email, Some("email"), None, Some(NetworkId("netId"))) shouldBe
          Right(Email("email", NetworkId("netId")))
      }
    }

    "Idp" should {
      "be left when missing id value" in {
        UniqueUserId.unsafeApply(IdType.IdpId, None, None, None).isLeft shouldBe true
      }

      "be right when has id value" in {
        UniqueUserId.unsafeApply(IdType.IdpId, Some("idpId"), None, None) shouldBe Right(Idp("idpId"))
      }
    }

    "External" should {
      "be left when missing externalId" in {
        UniqueUserId.unsafeApply(IdType.ExternalId, None, None, None).isLeft shouldBe true
      }

      "be right when has externalId" in {
        val externalId = ExternalId("subject", "id")
        UniqueUserId.unsafeApply(IdType.ExternalId, None, Some(externalId), None) shouldBe Right(External(externalId))
      }
    }

  }

}